# Basic Logic Gates

Welcome to the foundation of digital logic! In this module, you'll learn about the fundamental building blocks that make all digital systems possible.

## What Are Logic Gates?

Logic gates are the basic building blocks of digital circuits. They perform simple logical operations on one or more binary inputs to produce a single binary output. Think of them as the "atoms" of digital logic - everything more complex is built from these simple components.

## The Universal Gate: NAND

In this course, we start with the **NAND gate** because it's what we call a "universal gate." This means you can build any other logic gate using only NAND gates! This is exactly what happens inside real computer processors.

### NAND Gate Truth Table

| Input A | Input B | Output |
| ------- | ------- | ------ |
| 0       | 0       | 1      |
| 0       | 1       | 1      |
| 1       | 0       | 1      |
| 1       | 1       | 0      |

The NAND gate outputs 0 only when both inputs are 1. Otherwise, it outputs 1.

## Gates You'll Build

In this module, you'll construct these fundamental gates using only NAND gates:

### AND Gate

- **Logic**: Output is 1 only when both inputs are 1
- **Construction**: Use a NAND gate followed by a NOT gate (which is also made from a NAND!)
- **Formula**: A AND B = NOT(A NAND B)

### XOR Gate (Exclusive OR)

- **Logic**: Output is 1 only when inputs are different
- **Construction**: More complex - requires multiple NAND gates
- **Use**: Essential for arithmetic operations like addition

## Why Start Here?

Understanding these basic gates is crucial because:

1. **Foundation**: Every digital device uses these gates
2. **Universality**: You can build anything with just NAND gates
3. **Real-world relevance**: This is how actual computer chips work
4. **Problem-solving**: You'll develop logical thinking skills

## Learning Approach

Each challenge in this module will:

- Give you a target gate to build
- Provide only NAND gates as building blocks
- Test your circuit with all possible input combinations
- Unlock new tools for future modules

Ready to start building? Let's create your first logic gate!
