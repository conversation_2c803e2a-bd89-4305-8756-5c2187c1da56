# Level 1: Building an AND Gate

## Prerequisites

- A basic understanding of what a logic gate is.
- You should know that a **NAND** gate is a "universal" gate.

## The Goal

Our mission is to build one of the most fundamental logic gates, the **AND** gate, using only **NAND** gates.

An AND gate's logic is simple: _"The output is 1 only if input A AND input B are both 1."_

## The Grand Scheme

Why are we doing this? By proving that we can build other gates from a single type of gate (NAND), we are taking the first step in building a complex system from an incredibly simple, repeatable unit. This is the foundational principle of all digital electronics!

## How to Build It

You will need three NAND gates. Think about how you can use one NAND gate to act as a **NOT** gate (an inverter).

_Hint: `A AND B = NOT (A NAND B)`_
