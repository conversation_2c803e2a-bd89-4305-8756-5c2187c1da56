# Build a Half-Adder

Welcome to your first arithmetic circuit! The half-adder is the foundation of all computer arithmetic - it's how computers add numbers at the most basic level.

## What is a Half-Adder?

A half-adder is a digital circuit that adds two single bits together. It produces two outputs:

- **Sum**: The result of the addition (without carry from previous position)
- **Carry**: The overflow that goes to the next higher bit position

Think of it like adding two single digits in decimal, but in binary.

## The Challenge

Your task is to build a half-adder using the logic gates you've unlocked:

- **Inputs**: Two switches (A and B)
- **Outputs**: Two LEDs (Sum and Carry)
- **Available tools**: AND gates, XOR gates, NAND gates, switches, and LEDs

## Truth Table

Here's what your half-adder should do:

| Input A | Input B | Sum Output | Carry Output |
| ------- | ------- | ---------- | ------------ |
| 0       | 0       | 0          | 0            |
| 0       | 1       | 1          | 0            |
| 1       | 0       | 1          | 0            |
| 1       | 1       | 0          | 1            |

## Understanding the Logic

Let's think about each output:

### Sum Output

The sum should be 1 when the inputs are different (one is 0, the other is 1). This is exactly what an **XOR gate** does!

- 0 XOR 0 = 0
- 0 XOR 1 = 1
- 1 XOR 0 = 1
- 1 XOR 1 = 0

### Carry Output

The carry should be 1 only when both inputs are 1. This is exactly what an **AND gate** does!

- 0 AND 0 = 0
- 0 AND 1 = 0
- 1 AND 0 = 0
- 1 AND 1 = 1

## Construction Strategy

1. **Connect inputs**: Place two switches for inputs A and B
2. **Sum circuit**: Connect both inputs to an XOR gate
3. **Carry circuit**: Connect both inputs to an AND gate
4. **Connect outputs**: Connect the XOR output to one LED (Sum) and the AND output to another LED (Carry)

## Testing Your Circuit

Test all four input combinations:

1. Both switches OFF (0,0) → Both LEDs should be OFF
2. First switch ON, second OFF (1,0) → Sum LED ON, Carry LED OFF
3. First switch OFF, second ON (0,1) → Sum LED ON, Carry LED OFF
4. Both switches ON (1,1) → Sum LED OFF, Carry LED ON

## Why This Matters

The half-adder is crucial because:

### Building Blocks

- **Full-adders**: Add three bits (two inputs plus carry from previous position)
- **Multi-bit adders**: Chain multiple adders to add larger numbers
- **ALU**: The arithmetic logic unit in processors uses these principles

### Real-World Connection

Every time your computer adds two numbers, it's using circuits based on this design. Your smartphone, laptop, and even smart appliances all use variations of this circuit billions of times per second!

### Binary Addition Example

When adding binary numbers like 101 + 011:

```
  101  (5 in decimal)
+ 011  (3 in decimal)
-----
 1000  (8 in decimal)
```

Each column uses a half-adder (for the rightmost) or full-adder (for others) to compute the result.

## Common Mistakes

- **Swapped outputs**: Make sure Sum connects to XOR and Carry connects to AND
- **Wrong gate types**: Double-check you're using XOR for Sum, not OR
- **Missing connections**: Ensure both inputs go to both gates

## Next Steps

Once you master the half-adder, you'll be ready for:

- **Full-adders**: Handle carry input from previous position
- **Multi-bit adders**: Add larger numbers
- **Subtractors**: Subtract using two's complement
- **Multipliers**: Repeated addition circuits

Ready to build the foundation of computer arithmetic? Let's create your half-adder!
