# Introduction to Arithmetic Circuits

## What Are Arithmetic Circuits?

Arithmetic circuits are digital logic circuits that perform mathematical operations on binary numbers. These circuits form the heart of every computer's **Arithmetic Logic Unit (ALU)** and enable computers to perform calculations, comparisons, and data processing.

## Binary Number System Review

### Binary Representation

In binary, we use only two digits: **0** and **1**

| Decimal | Binary | Powers of 2  |
| ------- | ------ | ------------ |
| 0       | 0      | 0            |
| 1       | 1      | 2⁰ = 1       |
| 2       | 10     | 2¹ = 2       |
| 3       | 11     | 2¹ + 2⁰      |
| 4       | 100    | 2² = 4       |
| 5       | 101    | 2² + 2⁰      |
| 6       | 110    | 2² + 2¹      |
| 7       | 111    | 2² + 2¹ + 2⁰ |

### Binary Addition Rules

Binary addition follows simple rules:

- **0 + 0 = 0**
- **0 + 1 = 1**
- **1 + 0 = 1**
- **1 + 1 = 10** (0 with carry of 1)

## Fundamental Arithmetic Building Blocks

### Half-Adder

The **half-adder** is the simplest arithmetic circuit. It adds two single bits.

**Inputs**: A, B  
**Outputs**: Sum, Carry

**Truth Table**:
| A | B | Sum | Carry |
|---|---|-----|-------|
| 0 | 0 | 0 | 0 |
| 0 | 1 | 1 | 0 |
| 1 | 0 | 1 | 0 |
| 1 | 1 | 0 | 1 |

**Logic**:

- **Sum = A ⊕ B** (XOR operation)
- **Carry = A ∧ B** (AND operation)

**Why "Half"?**: It can't handle a carry input from a previous stage.

### Full-Adder

The **full-adder** extends the half-adder to handle carry inputs, enabling multi-bit addition.

**Inputs**: A, B, Carry_in  
**Outputs**: Sum, Carry_out

**Truth Table**:
| A | B | Cin | Sum | Cout |
|---|---|-----|-----|------|
| 0 | 0 | 0 | 0 | 0 |
| 0 | 0 | 1 | 1 | 0 |
| 0 | 1 | 0 | 1 | 0 |
| 0 | 1 | 1 | 0 | 1 |
| 1 | 0 | 0 | 1 | 0 |
| 1 | 0 | 1 | 0 | 1 |
| 1 | 1 | 0 | 0 | 1 |
| 1 | 1 | 1 | 1 | 1 |

**Logic**:

- **Sum = A ⊕ B ⊕ Cin**
- **Carry_out = (A ∧ B) ∨ (Cin ∧ (A ⊕ B))**

## Multi-Bit Arithmetic

### Ripple-Carry Adder

Chains multiple full-adders to add multi-bit numbers.

**4-bit Example**:

```
A3 B3    A2 B2    A1 B1    A0 B0
 │  │     │  │     │  │     │  │
 ▼  ▼     ▼  ▼     ▼  ▼     ▼  ▼
┌─────┐  ┌─────┐  ┌─────┐  ┌─────┐
│ FA  │◄─│ FA  │◄─│ FA  │◄─│ FA  │◄─ 0
└─────┘  └─────┘  └─────┘  └─────┘
   │        │        │        │
   ▼        ▼        ▼        ▼
  S3       S2       S1       S0
```

**Characteristics**:

- **Simple design**: Just chain full-adders together
- **Slow operation**: Carry must propagate through all stages
- **Delay**: Proportional to number of bits (n × gate_delay)

### Carry Propagation

The main limitation of ripple-carry adders is **carry propagation delay**.

**Example**: Adding 1111 + 0001

1. Rightmost bit: 1 + 1 = 0, carry = 1
2. Next bit: 1 + 0 + 1 = 0, carry = 1
3. Next bit: 1 + 0 + 1 = 0, carry = 1
4. Leftmost bit: 1 + 0 + 1 = 0, carry = 1

Each stage must wait for the previous carry!

## Advanced Arithmetic Concepts

### Subtraction

Binary subtraction can be performed using:

1. **Two's Complement**: Convert subtraction to addition

   - To subtract B from A: A + (-B)
   - Two's complement of B: Invert all bits and add 1

2. **Borrow Method**: Similar to decimal subtraction
   - More complex to implement in hardware

### Multiplication

Binary multiplication uses **shift and add**:

- **Partial products**: Multiply by each bit position
- **Shift left**: For each bit position (×2, ×4, ×8, etc.)
- **Add results**: Sum all partial products

**Example**: 5 × 3 = 101 × 011

```
    101  (5)
  × 011  (3)
  -----
    101  (5 × 1)
   101   (5 × 1, shifted)
  000    (5 × 0, shifted)
  -----
  1111   (15 in decimal)
```

### Division

Binary division uses **shift and subtract**:

- Similar to long division in decimal
- More complex than addition/multiplication
- Often implemented using iterative algorithms

## Arithmetic Logic Unit (ALU)

The ALU combines arithmetic and logic operations:

### Common ALU Operations

- **Arithmetic**: ADD, SUB, INC, DEC
- **Logic**: AND, OR, XOR, NOT
- **Shift**: SHL, SHR, ROL, ROR
- **Compare**: EQ, LT, GT, LE, GE

### ALU Control

- **Operation select**: Binary code specifies operation
- **Flag outputs**: Zero, Carry, Overflow, Sign
- **Conditional operations**: Based on flag states

## Real-World Applications

### Computer Processors

- **Integer arithmetic**: All basic math operations
- **Address calculation**: Memory addressing, indexing
- **Control flow**: Branch conditions, loop counters

### Digital Signal Processing

- **Audio processing**: Sample arithmetic, filtering
- **Image processing**: Pixel operations, transformations
- **Communications**: Error correction, modulation

### Embedded Systems

- **Sensor data**: Analog-to-digital conversion
- **Control systems**: PID controllers, feedback loops
- **Real-time processing**: Time-critical calculations

## Performance Considerations

### Speed Optimization

- **Carry-lookahead adders**: Faster carry propagation
- **Parallel processing**: Multiple operations simultaneously
- **Pipeline architecture**: Overlap operation stages

### Power Optimization

- **Clock gating**: Disable unused arithmetic units
- **Voltage scaling**: Lower voltage for non-critical paths
- **Approximate computing**: Trade accuracy for power savings

### Area Optimization

- **Shared resources**: Reuse arithmetic units
- **Bit-serial processing**: Process one bit at a time
- **Lookup tables**: Pre-computed results for small inputs

## Building Your 8-Bit Computer

In your computer project, arithmetic circuits will be used for:

1. **CPU Core**: Addition, subtraction, logical operations
2. **Memory Addressing**: Calculate memory locations
3. **Instruction Processing**: Decode and execute instructions
4. **I/O Operations**: Data formatting and conversion

## Next Steps

Now you're ready to build:

1. **Half-Adder**: Your first arithmetic circuit
2. **Full-Adder**: Handle carry propagation
3. **Multi-bit Adders**: 2-bit, 4-bit, 8-bit addition
4. **Complete ALU**: Combine arithmetic and logic

> **Remember**: Every calculation your computer performs - from simple addition to complex graphics rendering - ultimately relies on these fundamental arithmetic building blocks!
