# Basic Boolean Operations

Let's explore the three fundamental Boolean operations that correspond to our basic logic gates.

## AND Operation (·)

The AND operation returns TRUE only when both operands are TRUE.

**Notation:** A · B or A ∧ B or A AND B

**Truth Table:**
| A | B | A · B |
|---|---|-------|
| 0 | 0 |   0   |
| 0 | 1 |   0   |
| 1 | 0 |   0   |
| 1 | 1 |   1   |

## OR Operation (+)

The OR operation returns TRUE when at least one operand is TRUE.

**Notation:** A + B or A ∨ B or A OR B

**Truth Table:**
| A | B | A + B |
|---|---|-------|
| 0 | 0 |   0   |
| 0 | 1 |   1   |
| 1 | 0 |   1   |
| 1 | 1 |   1   |

## NOT Operation (')

The NOT operation returns the opposite of the operand.

**Notation:** A' or Ā or ¬A or NOT A

**Truth Table:**
| A | A' |
|---|----| 
| 0 | 1  |
| 1 | 0  |

## Important Notes

- In Boolean algebra, + means OR (not addition!)
- · means AND (not multiplication!)
- These operations follow specific rules that we'll explore next
