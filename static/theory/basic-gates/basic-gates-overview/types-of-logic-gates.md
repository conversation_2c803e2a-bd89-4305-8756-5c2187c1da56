# Basic Types of Logic Gates

There are several types of logic gates, each with its own behavior. Let's explore the most fundamental ones:

## AND Gate
The AND gate outputs **1** only when ALL inputs are **1**.

**Truth Table:**
| Input A | Input B | Output |
|---------|---------|--------|
|    0    |    0    |   0    |
|    0    |    1    |   0    |
|    1    |    0    |   0    |
|    1    |    1    |   1    |

**Real-world example:** A car that only starts when both the key is turned AND the seatbelt is fastened.

## OR Gate
The OR gate outputs **1** when AT LEAST ONE input is **1**.

**Truth Table:**
| Input A | Input B | Output |
|---------|---------|--------|
|    0    |    0    |   0    |
|    0    |    1    |   1    |
|    1    |    0    |   1    |
|    1    |    1    |   1    |

**Real-world example:** A doorbell that rings when someone presses the front button OR the back button.

## NOT Gate
The NOT gate (also called an inverter) outputs the OPPOSITE of its input.

**Truth Table:**
| Input | Output |
|-------|--------|
|   0   |   1    |
|   1   |   0    |

**Real-world example:** A motion sensor light that turns ON when it's dark (NOT bright).
