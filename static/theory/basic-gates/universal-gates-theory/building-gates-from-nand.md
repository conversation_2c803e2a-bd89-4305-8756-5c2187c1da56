# Building Other Gates from NAND

Now let's see the magic in action! Here's how you can build other gates using only NAND gates.

## Building a NOT Gate

To make a NOT gate from a NAND gate, simply connect both inputs together:

**Circuit:** Input A → NAND Gate (both inputs) → Output

**Why it works:**
- When A = 0: NAND(0,0) = 1 ✓
- When A = 1: NAND(1,1) = 0 ✓

This gives us the NOT behavior!

## Building an AND Gate

To make an AND gate, use two NAND gates:
1. First NAND gate takes your inputs
2. Second NAND gate acts as a NOT gate on the first gate's output

**Why it works:**
- NAND gives you "NOT AND"
- Adding another NOT gives you "NOT (NOT AND)" = AND

## Building an OR Gate

This one's trickier! You need three NAND gates:
1. Use two NAND gates as NOT gates on each input
2. Feed these inverted inputs into a third NAND gate

**Why it works:**
- By <PERSON>'s Law: OR(A,B) = NAND(NOT A, NOT B)

## The Challenge Ahead

In the upcoming challenges, you'll get to build these gates yourself! You'll start with NAND gates and construct AND, OR, and NOT gates step by step.

This hands-on experience will help you truly understand how all digital logic is built from these simple building blocks.
