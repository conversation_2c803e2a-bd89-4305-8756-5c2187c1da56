# Compound Logic Functions

Beyond basic AND, OR, and NOT gates, digital circuits use more sophisticated logic functions. These compound functions are essential building blocks for arithmetic, comparison, and data manipulation operations.

## XOR Gate (Exclusive OR)

The XOR gate is one of the most important compound logic functions, especially for arithmetic operations.

### XOR Truth Table

| A | B | A ⊕ B |
|---|---|-------|
| 0 | 0 | 0     |
| 0 | 1 | 1     |
| 1 | 0 | 1     |
| 1 | 1 | 0     |

**Key characteristic**: XOR outputs 1 only when inputs are **different**.

### Boolean Expression

**A ⊕ B = (A ∧ ¬B) ∨ (¬A ∧ B)**

This reads as: "A and not B, or not A and B"

### NAND Implementation Strategy

Building XOR from NAND gates requires 4 gates:

1. **Create NOT A**: NAND gate with both inputs connected to A
2. **Create NOT B**: NAND gate with both inputs connected to B  
3. **Create (A AND NOT B)**: Use NAND gates to implement this term
4. **Create (NOT A AND B)**: Use NAND gates to implement this term
5. **OR the results**: Use <PERSON>'s law with NAND gates

### Why XOR Matters

- **Addition circuits**: XOR performs binary addition (sum bit)
- **Comparison**: Detects when two bits are different
- **Parity checking**: Determines if a group of bits has even/odd count
- **Encryption**: XOR is fundamental to many cryptographic algorithms

## XNOR Gate (Equivalence)

XNOR is the complement of XOR - it outputs 1 when inputs are the **same**.

### XNOR Truth Table

| A | B | A ⊙ B |
|---|---|-------|
| 0 | 0 | 1     |
| 0 | 1 | 0     |
| 1 | 0 | 0     |
| 1 | 1 | 1     |

### Construction

**XNOR = NOT(XOR)**

Simply add a NOT gate after an XOR gate, or use De Morgan's laws to implement directly with NAND gates.

### Applications

- **Equality comparison**: Checking if two bits are equal
- **Error detection**: Comparing received vs. expected data
- **Control logic**: Enabling circuits when conditions match

## Multiplexer (MUX)

A multiplexer selects one of multiple inputs based on control signals.

### 2-to-1 MUX

**Inputs**: A, B, SELECT  
**Output**: A when SELECT=0, B when SELECT=1

**Boolean Expression**: OUT = (¬SELECT ∧ A) ∨ (SELECT ∧ B)

### Construction Steps

1. **Create NOT SELECT**: Invert the select signal
2. **Gate A**: AND input A with NOT SELECT
3. **Gate B**: AND input B with SELECT
4. **Combine**: OR the two results

### MUX Applications

- **Data routing**: Selecting between different data sources
- **Function implementation**: Any logic function can be built with MUX
- **Bus switching**: Connecting different devices to shared resources

## Demultiplexer (DEMUX)

A demultiplexer routes one input to one of multiple outputs.

### 1-to-2 DEMUX

**Inputs**: DATA, SELECT  
**Outputs**: OUT0, OUT1

**Logic**:
- OUT0 = DATA ∧ ¬SELECT
- OUT1 = DATA ∧ SELECT

### DEMUX Applications

- **Address decoding**: Routing signals to specific memory locations
- **Data distribution**: Sending data to selected destinations
- **Control signal generation**: Activating specific circuit blocks

## Design Patterns

### Hierarchical Construction

1. **Start simple**: Build basic gates (AND, OR, NOT)
2. **Medium complexity**: Combine into XOR, MUX, DEMUX
3. **Complex functions**: Create adders, comparators, decoders

### Modular Approach

- **Reusable blocks**: Design once, use many times
- **Standard interfaces**: Consistent input/output conventions
- **Testing isolation**: Debug individual modules separately

> **Looking Ahead**: These compound logic functions are the foundation for the arithmetic and logic unit (ALU) in your CPU. Master them now, and building your processor will be much easier!
