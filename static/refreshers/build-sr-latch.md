## Your Goal

Create an SR (Set-Reset) latch using NAND gates. This is the foundation of all memory elements.

## What You're Building

An **SR Latch** that:

- Stores one bit of information
- Has Set and Reset control inputs
- Provides Q and Q̄ (not Q) outputs
- Forms the basis for all flip-flops and memory

## Key Concepts

### SR Latch Truth Table (Active Low)

| S̄   | R̄   | Q   | Q̄   | Action           |
| --- | --- | --- | --- | ---------------- |
| 1   | 1   | Q   | Q̄   | Hold (no change) |
| 0   | 1   | 1   | 0   | Set (Q = 1)      |
| 1   | 0   | 0   | 1   | Reset (Q = 0)    |
| 0   | 0   | 1   | 1   | Forbidden        |

### Memory Behavior

- **Set**: Forces output Q to 1
- **Reset**: Forces output Q to 0
- **Hold**: Maintains previous state
- **Forbidden**: Both outputs become 1 (unstable)

### Why "Active Low"?

- Inputs are active when LOW (0)
- S̄ = 0 means "Set is active"
- R̄ = 0 means "Reset is active"
- Common in NAND-based circuits

## Construction Strategy

### Required Components

- **2 NAND Gates**: Cross-coupled for feedback
- **2 Switches**: Set and Reset inputs
- **2 LEDs**: Q and Q̄ outputs

### Cross-Coupled Design

The key is **positive feedback**:

- Output of first NAND feeds input of second NAND
- Output of second NAND feeds input of first NAND
- This creates a bistable circuit (two stable states)

## Step-by-Step Instructions

### Step 1: Place NAND Gates

1. Place first NAND gate (for Q output)
2. Place second NAND gate (for Q̄ output)
3. Position them for easy cross-coupling

### Step 2: Connect Cross-Coupling

1. Connect output of first NAND to one input of second NAND
2. Connect output of second NAND to one input of first NAND
3. This creates the feedback loop that stores state

### Step 3: Connect Control Inputs

1. Connect Set switch to remaining input of first NAND (Q side)
2. Connect Reset switch to remaining input of second NAND (Q̄ side)
3. Set controls Q output, Reset controls Q̄ output

### Step 4: Connect Outputs

1. Connect first NAND output to Q LED
2. Connect second NAND output to Q̄ LED
3. Q and Q̄ should always be opposite (except in forbidden state)

### Circuit Diagram

```
Set ──┐     ┌── Q
      │     │
    [NAND]──┼──┐
      │     │  │
      └─────┼──┘
            │
      ┌─────┼──┐
      │     │  │
    [NAND]──┼──┘
      │     │
Reset ┘     └── Q̄
```

## Testing Your SR Latch

### Test Sequence 1: Set Operation

1. Start with S=1, R=1 (both inactive)
2. Set S=0, R=1 (activate Set)
3. Verify Q=1, Q̄=0
4. Return S=1, R=1
5. Verify Q=1, Q̄=0 (state held)

### Test Sequence 2: Reset Operation

1. From previous state (Q=1)
2. Set S=1, R=0 (activate Reset)
3. Verify Q=0, Q̄=1
4. Return S=1, R=1
5. Verify Q=0, Q̄=1 (state held)

### Test Sequence 3: Forbidden State

1. Set S=0, R=0 (both active)
2. Observe Q=1, Q̄=1 (both high)
3. This is unstable - avoid in real circuits

## Understanding the Feedback

### Bistable Operation

- Two stable states: Q=1,Q̄=0 or Q=0,Q̄=1
- Feedback maintains current state
- External inputs can force state changes

### How Set Works

1. S=0 forces first NAND output (Q) to 1
2. Q=1 feeds back to second NAND
3. With R=1, second NAND outputs Q̄=0
4. Q̄=0 feeds back to first NAND, maintaining Q=1

### How Reset Works

1. R=0 forces second NAND output (Q̄) to 1
2. Q̄=1 feeds back to first NAND
3. With S=1, first NAND outputs Q=0
4. Q=0 feeds back to second NAND, maintaining Q̄=1

## Common Mistakes

### Incorrect Cross-Coupling

- **Problem**: Not connecting outputs to opposite inputs
- **Result**: No memory behavior, outputs don't hold state
- **Fix**: Each NAND output must feed the other NAND input

### Wrong Input Connections

- **Problem**: Connecting Set to Q̄ side, Reset to Q side
- **Result**: Set resets and Reset sets (backwards operation)
- **Fix**: Set connects to Q side, Reset connects to Q̄ side

### Misunderstanding Active Low

- **Problem**: Expecting S=1 to set, R=1 to reset
- **Result**: Confusion about when latch responds
- **Fix**: Remember inputs are active when LOW (0)

## Memory Fundamentals

### State Storage

- Latch "remembers" which input was last activated
- State persists until opposite input is activated
- Foundation of all computer memory

### Timing Independence

- SR latch is asynchronous (no clock needed)
- Responds immediately to input changes
- Basis for understanding clocked memory elements

## Real-World Applications

### Flip-Flop Building Block

- D flip-flops use SR latches internally
- Clock controls when SR latch can change
- Synchronous operation in digital systems

### Debouncing Circuits

- Mechanical switches "bounce" when pressed
- SR latch eliminates multiple transitions
- Provides clean digital signals

### Control Systems

- Start/Stop controls in machinery
- Emergency stop circuits
- State machines in automation

## Design Principles

### Positive Feedback

- Output reinforces input condition
- Creates stable states
- Fundamental to all memory circuits

### Complementary Outputs

- Q and Q̄ normally opposite
- Provides both true and inverted signals
- Useful for driving differential circuits

## Success Criteria

✅ Set input (S=0) forces Q=1, Q̄=0  
✅ Reset input (R=0) forces Q=0, Q̄=1  
✅ Hold state (S=1, R=1) maintains previous output  
✅ Cross-coupled NAND gates provide feedback

## Next Steps

Your SR latch enables:

- **D Flip-Flops**: Add clock control for synchronous operation
- **Memory Arrays**: Scale up to store multiple bits
- **State Machines**: Control sequential logic systems

The memory behavior you're creating here is the foundation of every bit stored in computer memory, from CPU registers to RAM!
