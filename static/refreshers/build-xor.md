## Your Goal

Build an XOR (Exclusive OR) gate using only NAND gates. The XOR gate should output 1 only when the inputs are different.

## Key Concept

**XOR outputs 1 when inputs are different, 0 when inputs are the same.**

## XOR Truth Table

| Input A | Input B | XOR Output |
| ------- | ------- | ---------- |
| 0       | 0       | 0          |
| 0       | 1       | 1          |
| 1       | 0       | 1          |
| 1       | 1       | 0          |

## Construction Strategy

XOR requires 4 NAND gates. Think of it as:
**XOR = (A AND NOT B) OR (NOT A AND B)**

1. **Create NOT A**: Use a NAND gate with both inputs connected to A
2. **Create NOT B**: Use a NAND gate with both inputs connected to B
3. **Create A AND NOT B**: Use a NAND gate with A and NOT B, then invert
4. **Create NOT A AND B**: Use a NAND gate with NOT A and B, then invert
5. **Combine with OR**: Use a final NAND gate to combine the results

## Alternative Approach

You can also use the NAND-only implementation:

- Connect A and B to first NAND gate
- Connect A to second NAND gate (both inputs)
- Connect B to third NAND gate (both inputs)
- Connect outputs of gates 1, 2, and 3 to final NAND gate

## Testing

- (0,0) → LED OFF (same inputs)
- (0,1) → LED ON (different inputs)
- (1,0) → LED ON (different inputs)
- (1,1) → LED OFF (same inputs)

## Tip

XOR is more complex than AND - don't worry if it takes several attempts to get the wiring right!
