## Your Goal

Build a half-adder that adds two single bits and produces a Sum and Carry output.

## Key Concept

A half-adder performs binary addition of two bits:

- **Sum**: The result bit (use XOR gate)
- **Carry**: The overflow bit (use AND gate)

## Truth Table

| Input A | Input B | Sum (A⊕B) | Carry (A∧B) |
| ------- | ------- | --------- | ----------- |
| 0       | 0       | 0         | 0           |
| 0       | 1       | 1         | 0           |
| 1       | 0       | 1         | 0           |
| 1       | 1       | 0         | 1           |

## Construction Strategy

1. **Sum Output**: Connect both inputs to an XOR gate → Connect to first LED
2. **Carry Output**: Connect both inputs to an AND gate → Connect to second LED

## Component Requirements

- 2 input switches (A and B)
- 1 XOR gate (for Sum)
- 1 AND gate (for Carry)
- 2 LEDs (Sum and Carry outputs)

## Binary Addition Examples

- 0 + 0 = 00 (Sum=0, Carry=0)
- 0 + 1 = 01 (Sum=1, Carry=0)
- 1 + 0 = 01 (Sum=1, Carry=0)
- 1 + 1 = 10 (Sum=0, Carry=1)

## Testing

Verify each combination:

- Both switches OFF → Both LEDs OFF
- One switch ON → Sum LED ON, Carry LED OFF
- Both switches ON → Sum LED OFF, Carry LED ON

## Real-World Connection

This is exactly how computers add numbers! Multiple half-adders and full-adders chain together to add larger binary numbers.

## Tip

Make sure you have two separate output LEDs - one for Sum and one for Carry!
