## Your Goal

Expand to an 8-bit register using your 4-bit register components or individual flip-flops.

## What You're Building

An **8-bit register** that:

- Stores a full byte (8 bits) of data
- Represents the standard computer word size
- Provides foundation for CPU register files
- Demonstrates modular design principles

## Key Concepts

### Computer Word Size

8 bits = 1 byte, the fundamental unit of computer memory:

- **Range**: Can store values 0-255 (decimal)
- **Applications**: ASCII characters, small integers, control codes
- **Significance**: Standard addressable unit in most computers

### Modular Construction

Build using existing components:

- **Option 1**: Two 4-bit registers in parallel
- **Option 2**: Eight individual D flip-flops
- **Scalability**: Same principles apply to 16-bit, 32-bit, 64-bit registers

## Construction Strategy

### Required Components

- **2 × 4-bit Registers**: High and low nibbles
- **8 Data Switches**: D7, D6, D5, D4, D3, D2, D1, D0
- **1 Clock Switch**: Shared clock signal
- **1 Load Switch**: Load enable control
- **8 LEDs**: Q7, Q6, Q5, Q4, Q3, Q2, Q1, Q0 outputs

### Register Architecture

```
High Nibble (4-bit Register)    Low Nibble (4-bit Register)
D7 D6 D5 D4                     D3 D2 D1 D0
│  │  │  │                      │  │  │  │
└──┼──┼──┼──────────────────────┼──┼──┼──┘
   │  │  │                      │  │  │
   Clock ──────────────────────── Clock
   Load ────────────────────────── Load
   │  │  │                      │  │  │
   Q7 Q6 Q5 Q4                  Q3 Q2 Q1 Q0
```

### Bit Significance

| Bit | Weight | Hex | Purpose                     |
| --- | ------ | --- | --------------------------- |
| D7  | 128    | 80  | Most Significant Bit (MSB)  |
| D6  | 64     | 40  |                             |
| D5  | 32     | 20  |                             |
| D4  | 16     | 10  |                             |
| D3  | 8      | 08  |                             |
| D2  | 4      | 04  |                             |
| D1  | 2      | 02  |                             |
| D0  | 1      | 01  | Least Significant Bit (LSB) |

## Step-by-Step Instructions

### Step 1: Place 4-bit Registers

1. Place first 4-bit register (for high nibble)
2. Place second 4-bit register (for low nibble)
3. Position them side by side for easy wiring

### Step 2: Connect High Nibble (Bits 7-4)

1. Connect D7 switch to first register D3 input
2. Connect D6 switch to first register D2 input
3. Connect D5 switch to first register D1 input
4. Connect D4 switch to first register D0 input

### Step 3: Connect Low Nibble (Bits 3-0)

1. Connect D3 switch to second register D3 input
2. Connect D2 switch to second register D2 input
3. Connect D1 switch to second register D1 input
4. Connect D0 switch to second register D0 input

### Step 4: Connect Control Signals

1. Connect Clock switch to both registers' clock inputs
2. Connect Load switch to both registers' load inputs
3. Both registers must operate synchronously

### Step 5: Connect Outputs

1. Connect first register outputs to Q7, Q6, Q5, Q4 LEDs
2. Connect second register outputs to Q3, Q2, Q1, Q0 LEDs
3. Arrange LEDs in bit order for easy reading

## Testing Your 8-bit Register

### Test Sequence 1: Load Maximum Value

1. Set Load = 1
2. Set all data inputs to 1: D7=1, D6=1, ..., D0=1
3. Apply clock pulse
4. Verify all outputs are 1 (binary 11111111 = 255 decimal)

### Test Sequence 2: Load Minimum Value

1. Set Load = 1
2. Set all data inputs to 0: D7=0, D6=0, ..., D0=0
3. Apply clock pulse
4. Verify all outputs are 0 (binary 00000000 = 0 decimal)

### Test Sequence 3: Load Pattern

1. Set Load = 1
2. Set alternating pattern: D7=1, D6=0, D5=1, D4=0, D3=1, D2=0, D1=1, D0=0
3. Apply clock pulse
4. Verify outputs: Q7=1, Q6=0, Q5=1, Q4=0, Q3=1, Q2=0, Q1=1, Q0=0
5. Binary 10101010 = 170 decimal = AA hex

### Test Sequence 4: Hold Test

1. Set Load = 0
2. Change all data inputs to opposite values
3. Apply clock pulse
4. Verify outputs remain unchanged
5. Register should ignore new data when Load=0

## Understanding 8-bit Values

### Binary to Decimal Conversion

Example: 10110011

- 1×128 + 0×64 + 1×32 + 1×16 + 0×8 + 0×4 + 1×2 + 1×1
- 128 + 32 + 16 + 2 + 1 = 179 decimal

### Common 8-bit Values

| Binary   | Decimal | Hex | Meaning                   |
| -------- | ------- | --- | ------------------------- |
| 00000000 | 0       | 00  | Minimum value             |
| 01111111 | 127     | 7F  | Maximum positive (signed) |
| 10000000 | 128     | 80  | Minimum negative (signed) |
| 11111111 | 255     | FF  | Maximum value             |

## Common Mistakes

### Nibble Reversal

- **Problem**: Connecting high nibble to low register, vice versa
- **Result**: Data appears byte-swapped
- **Fix**: D7-D4 → first register, D3-D0 → second register

### Control Signal Splitting

- **Problem**: Forgetting to connect clock/load to both registers
- **Result**: Only half the register updates
- **Fix**: Clock and Load must go to both 4-bit registers

### Output Ordering

- **Problem**: Connecting outputs in wrong bit order
- **Result**: Data appears scrambled
- **Fix**: Maintain bit order Q7, Q6, Q5, Q4, Q3, Q2, Q1, Q0

## Design Alternatives

### Individual Flip-Flops

Instead of 4-bit registers, you could use:

- 8 individual D flip-flops
- More wiring but same functionality
- Better understanding of underlying structure

### Hierarchical Design

Your approach demonstrates:

- **Modularity**: Reusing 4-bit building blocks
- **Scalability**: Easy to extend to 16-bit, 32-bit
- **Maintainability**: Easier to debug and modify

## Real-World Applications

### CPU Registers

- **Data Registers**: Store operands and results
- **Address Registers**: Hold memory addresses
- **Instruction Register**: Store current instruction

### Memory Interface

- **Data Bus**: 8-bit data transfer
- **Address Bus**: Memory location selection
- **Control Bus**: Read/write signals

### I/O Operations

- **Character Data**: ASCII codes (0-127)
- **Control Codes**: Device commands
- **Status Information**: System state

## Success Criteria

✅ All 8 bits load simultaneously when Load=1  
✅ Register holds 8-bit value when Load=0  
✅ Outputs correctly display stored byte value  
✅ Both nibbles operate synchronously

## Next Steps

Your 8-bit register enables:

- **Register Files**: Arrays of 8-bit registers
- **Memory Systems**: 8-bit data storage
- **CPU Design**: Processor data paths

You've now built the fundamental storage unit used in 8-bit computers like the classic 6502, Z80, and 8080 processors!
