## Your Goal

Use NAND gates to construct an OR gate. An OR gate outputs 1 when at least one of its inputs is 1.

## What You're Building

An **OR gate** that:

- Outputs 1 when input A is 1, OR input B is 1, OR both are 1
- Only outputs 0 when both inputs are 0
- Demonstrates <PERSON>'s laws in practice
- Forms a fundamental building block for digital logic

## Key Concepts

### OR Gate Truth Table

| A   | B   | Output |
| --- | --- | ------ |
| 0   | 0   | 0      |
| 0   | 1   | 1      |
| 1   | 0   | 1      |
| 1   | 1   | 1      |

### De <PERSON>'s Law

The OR gate can be constructed from NAND gates using:
**A + B = ¬(¬A · ¬B)**

This means:

- "A OR B" equals "NOT(NOT A AND NOT B)"
- First invert both inputs
- Then NAND the inverted inputs

## Construction Strategy

### Required Components

- **3 NAND Gates**: Two for input inversion, one for final NAND
- **2 Switches**: Input A and Input B
- **1 LED**: Output display

### Step-by-Step Construction

#### Step 1: Invert Input A

1. Place first NAND gate
2. Connect Input A switch to both inputs of this NAND gate
3. This creates NOT A (¬A)

#### Step 2: Invert Input B

1. Place second NAND gate
2. Connect Input B switch to both inputs of this NAND gate
3. This creates NOT B (¬B)

#### Step 3: NAND the Inverted Inputs

1. Place third NAND gate
2. Connect output of first NAND (¬A) to one input of third NAND
3. Connect output of second NAND (¬B) to other input of third NAND
4. Connect output of third NAND to LED

### Circuit Diagram

```
A ──┬─[NAND]─┐
    └─[NAND]─┘ ¬A
              │
              └─[NAND]── Output (A + B)
              │
B ──┬─[NAND]─┘ ¬B
    └─[NAND]─┘
```

## Testing Your OR Gate

### Test Sequence

1. **Both inputs LOW**: A=0, B=0 → Output should be 0
2. **A HIGH, B LOW**: A=1, B=0 → Output should be 1
3. **A LOW, B HIGH**: A=0, B=1 → Output should be 1
4. **Both inputs HIGH**: A=1, B=1 → Output should be 1

### Expected Behavior

- Output is 1 whenever at least one input is 1
- Output is 0 only when both inputs are 0
- This is the logical OR function

## Common Mistakes

### Forgetting Input Inversion

- **Problem**: Connecting inputs directly to final NAND
- **Result**: Creates NAND gate instead of OR gate
- **Fix**: Each input must be inverted first using NAND gates

### Incorrect NAND Connections

- **Problem**: Not connecting both inputs of inverting NAND gates
- **Result**: NAND gates don't function as inverters
- **Fix**: Both inputs of each inverting NAND must be connected to same signal

### Wrong Final Connection

- **Problem**: Using AND instead of NAND for final stage
- **Result**: Creates inverted OR (NOR) instead of OR
- **Fix**: Final stage must be NAND to complete De Morgan's transformation

## Understanding De Morgan's Laws

### Mathematical Proof

Starting with De Morgan's law: **A + B = ¬(¬A · ¬B)**

1. **¬A**: Invert input A using NAND gate
2. **¬B**: Invert input B using NAND gate
3. **¬A · ¬B**: AND the inverted inputs
4. **¬(¬A · ¬B)**: Invert the result (NAND does this automatically)

### Why This Works

- NAND gate naturally provides the final inversion
- Two inversions cancel out, leaving us with OR function
- This is a fundamental principle in digital logic design

## Real-World Applications

### Logic Circuits

- **Multiplexers**: Route data based on select signals
- **Decoders**: Convert binary codes to output lines
- **Control Logic**: Enable operations when conditions are met

### Computer Architecture

- **Instruction Decoding**: Determine which operation to perform
- **Interrupt Handling**: Respond to multiple interrupt sources
- **Bus Arbitration**: Grant access to shared resources

## Success Criteria

✅ Output is 1 when A=0, B=1  
✅ Output is 1 when A=1, B=0  
✅ Output is 1 when A=1, B=1  
✅ Output is 0 only when A=0, B=0

## Next Steps

Your OR gate will be essential for:

- **Full Adder**: Combining carry signals
- **Multiplexers**: Data routing logic
- **Control Circuits**: Conditional operations

The OR function you're building here is one of the three fundamental Boolean operations (AND, OR, NOT) that form the basis of all digital computation!
