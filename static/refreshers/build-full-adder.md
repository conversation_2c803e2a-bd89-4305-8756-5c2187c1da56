## Your Goal

Create a full adder that adds three bits (A, B, Carry-in) and produces Sum and Carry-out. Use the gates you've unlocked.

## What You're Building

A **Full Adder** that:

- Adds three single bits: A, B, and Carry-in
- Produces Sum and Carry-out outputs
- Extends the half-adder to handle carry chains
- Forms the building block for multi-bit arithmetic

## Key Concepts

### Full Adder Truth Table

| A   | B   | Cin | Sum | Cout |
| --- | --- | --- | --- | ---- |
| 0   | 0   | 0   | 0   | 0    |
| 0   | 0   | 1   | 1   | 0    |
| 0   | 1   | 0   | 1   | 0    |
| 0   | 1   | 1   | 0   | 1    |
| 1   | 0   | 0   | 1   | 0    |
| 1   | 0   | 1   | 0   | 1    |
| 1   | 1   | 0   | 0   | 1    |
| 1   | 1   | 1   | 1   | 1    |

### Full Adder Logic

- **Sum = A ⊕ B ⊕ Cin** (XOR of all three inputs)
- **Carry-out = AB + Cin(A ⊕ B)** (majority function)

### Why Three Inputs?

- **A, B**: The two bits being added
- **Carry-in**: Carry from previous bit position
- Essential for multi-bit addition chains

## Construction Strategy

### Required Components

- **2 XOR Gates**: For sum calculation
- **2 AND Gates**: For carry generation
- **1 OR Gate**: For final carry combination
- **3 Switches**: A, B, Carry-in inputs
- **2 LEDs**: Sum and Carry-out outputs

### Method 1: Two Half-Adders + OR Gate

#### Step 1: First Half-Adder (A + B)

1. Use XOR gate: A ⊕ B → Partial Sum
2. Use AND gate: A · B → Partial Carry

#### Step 2: Second Half-Adder (Partial Sum + Cin)

1. Use XOR gate: (A ⊕ B) ⊕ Cin → Final Sum
2. Use AND gate: (A ⊕ B) · Cin → Second Carry

#### Step 3: Combine Carries

1. Use OR gate: (A · B) + ((A ⊕ B) · Cin) → Final Carry-out

### Method 2: Direct Implementation

#### Sum Circuit

1. Connect A and B to first XOR gate
2. Connect XOR output and Cin to second XOR gate
3. Second XOR output is the Sum

#### Carry Circuit

1. Connect A and B to first AND gate → A·B
2. Connect A and B to XOR gate → A⊕B
3. Connect XOR output and Cin to second AND gate → (A⊕B)·Cin
4. Connect both AND outputs to OR gate → Final Carry-out

## Step-by-Step Instructions

### Step 1: Build Sum Logic

1. Place first XOR gate for A ⊕ B
2. Connect A switch to first input
3. Connect B switch to second input
4. Place second XOR gate for final sum
5. Connect first XOR output to second XOR first input
6. Connect Cin switch to second XOR second input
7. Connect second XOR output to Sum LED

### Step 2: Build Carry Logic

1. Place AND gate for A·B
2. Connect A and B switches to this AND gate
3. Place second AND gate for (A⊕B)·Cin
4. Connect first XOR output to second AND first input
5. Connect Cin switch to second AND second input
6. Place OR gate for final carry
7. Connect both AND outputs to OR gate inputs
8. Connect OR output to Carry-out LED

## Testing Your Full Adder

### Test Cases

1. **0 + 0 + 0 = 0**: Sum=0, Cout=0
2. **0 + 0 + 1 = 1**: Sum=1, Cout=0
3. **0 + 1 + 1 = 10**: Sum=0, Cout=1 (binary 10 = decimal 2)
4. **1 + 1 + 1 = 11**: Sum=1, Cout=1 (binary 11 = decimal 3)

### Verification Strategy

- Test all 8 possible input combinations
- Verify sum matches expected binary addition
- Confirm carry-out for results ≥ 2

## Understanding the Logic

### Sum Generation

- XOR gives 1 when odd number of inputs are 1
- Perfect for binary addition sum bit
- A ⊕ B ⊕ Cin handles all three inputs

### Carry Generation

- Carry occurs when at least 2 of 3 inputs are 1
- A·B: Both A and B are 1
- (A⊕B)·Cin: Exactly one of A,B is 1 AND Cin is 1
- OR combines both carry conditions

## Common Mistakes

### Incorrect Sum Logic

- **Problem**: Using AND instead of XOR for sum
- **Result**: Sum is wrong for most input combinations
- **Fix**: Sum must use XOR gates for proper addition

### Missing Carry Paths

- **Problem**: Not including all carry generation paths
- **Result**: Carry-out incorrect for some inputs
- **Fix**: Must include both A·B and (A⊕B)·Cin paths

### Wrong Gate Connections

- **Problem**: Swapping inputs or outputs
- **Result**: Incorrect truth table behavior
- **Fix**: Carefully trace each signal path

## Real-World Applications

### Multi-Bit Arithmetic

- **8-bit Adder**: Chain 8 full adders together
- **ALU Design**: Core component of arithmetic logic unit
- **Processor Design**: Essential for CPU arithmetic operations

### Digital Signal Processing

- **Accumulation**: Sum multiple values
- **Filtering**: Weighted sum calculations
- **Correlation**: Pattern matching operations

## Design Variations

### Ripple Carry Adder

- Chain full adders with carry connections
- Simple but slower for large word sizes
- Carry must propagate through all stages

### Carry Lookahead

- Parallel carry generation
- Faster but more complex
- Used in high-performance processors

## Success Criteria

✅ Correct sum for all 8 input combinations  
✅ Correct carry-out for all 8 input combinations  
✅ Uses XOR gates for sum generation  
✅ Uses AND and OR gates for carry generation

## Next Steps

Your full adder enables:

- **Multi-bit Adders**: Chain together for 8-bit, 16-bit addition
- **Subtraction**: Use with two's complement
- **Arithmetic Logic Unit**: Core ALU component

The full adder you're building is the fundamental arithmetic building block used in every computer processor!
