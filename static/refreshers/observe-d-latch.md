## Your Goal

Understand how the D-Latch works by observing its behavior with different input combinations.

## Key Concept

The D-Latch is a **memory element** - it can store one bit of information. Unlike combinational circuits, its output depends on both current inputs AND previous state.

## D-Latch Pins

- **D (Data)**: The value you want to store
- **E (Enable)**: Controls when the latch can change its stored value
- **Q (Output)**: The stored value

## Operating Modes

### Transparent Mode (E = 1)

When Enable is HIGH:

- Q immediately follows D
- D = 0 → Q = 0
- D = 1 → Q = 1
- The latch is "transparent" - data flows through

### Memory Mode (E = 0)

When Enable is LOW:

- Q holds its previous value
- Changes to D have no effect
- The latch "remembers" the last value when E was high

## Experiment Sequence

Try this step-by-step:

1. **Set D = 0, E = 1** → Q = 0 (transparent)
2. **Set D = 1, E = 1** → Q = 1 (follows D)
3. **Set E = 0** → Q = 1 (remembers!)
4. **Set D = 0, E = 0** → Q = 1 (still remembers!)
5. **Set E = 1** → Q = 0 (now follows D again)

## The "Aha!" Moment

Step 4 is where you see memory in action! Even though D changed to 0, Q remained 1 because the Enable was low. The latch remembered the previous value.

## Setup Instructions

1. Connect a SWITCH to the D input
2. Connect a CLOCK to the E input
3. Observe the Q output
4. Try different timing patterns

## Key Insight

This is the foundation of all computer memory - from CPU registers to RAM. Every bit stored in your computer uses this basic principle!

## No Verification Needed

This challenge is about observation and understanding. Take your time to experiment and really grasp how memory works.
