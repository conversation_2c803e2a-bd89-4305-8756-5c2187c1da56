## Your Goal

Build an AND gate using only NAND gates. The AND gate should output 1 only when both inputs are 1.

## Key Concept

**AND = NOT(NAND)**

Since NAND is the opposite of AND, you can create an AND gate by inverting the output of a NAND gate.

## Construction Strategy

1. **Connect inputs**: Wire both input switches to a NAND gate
2. **Invert output**: Connect the NAND output to another NAND gate (with both inputs tied together to create a NOT gate)
3. **Final output**: Connect the second NAND output to your LED

## Truth Table Reminder

| Input A | Input B | NAND Output | Final AND Output |
| ------- | ------- | ----------- | ---------------- |
| 0       | 0       | 1           | 0                |
| 0       | 1       | 1           | 0                |
| 1       | 0       | 1           | 0                |
| 1       | 1       | 0           | 1                |

## Testing

Test all four input combinations:

- (0,0) → LED should be OFF
- (0,1) → LED should be OFF
- (1,0) → LED should be OFF
- (1,1) → LED should be ON

## Hint

Remember: To make a NOT gate from a NAND gate, connect both inputs of the NAND gate together!
