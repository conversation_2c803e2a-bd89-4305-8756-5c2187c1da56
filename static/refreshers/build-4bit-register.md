## Your Goal

Combine 4 D flip-flops to create a 4-bit storage register with load enable control.

## What You're Building

A **4-bit register** that:

- Stores 4 bits of data simultaneously
- Updates all bits on the same clock edge
- Has load enable control for selective updates
- Forms the building block for larger registers

## Key Concepts

### Parallel Storage

Unlike serial storage (one bit at a time), a register provides:

- **Parallel Input**: All 4 data bits loaded simultaneously
- **Parallel Output**: All 4 stored bits available at once
- **Synchronous Operation**: All flip-flops share the same clock

### Load Enable Control

The Load signal determines when new data is stored:

- **Load = 1**: New data loaded on next clock edge
- **Load = 0**: Register holds current value, ignores new data
- **Clock Gating**: Load signal controls when flip-flops update

## Construction Strategy

### Required Components

- **4 D Flip-Flops**: One for each bit
- **4 Data Switches**: D3, D2, D1, D0 inputs
- **1 Clock Switch**: Shared clock signal
- **1 Load Switch**: Load enable control
- **4 LEDs**: Q3, Q2, Q1, Q0 outputs

### Register Architecture

```
D3 ──┐    ┌─── Q3
D2 ──┼────┼─── Q2
D1 ──┼────┼─── Q1
D0 ──┘    └─── Q0
     │    │
Clock──────┤
Load───────┘
```

### Truth Table

| D3  | D2  | D1  | D0  | Clock | Load | Q3  | Q2  | Q1  | Q0  | Action    |
| --- | --- | --- | --- | ----- | ---- | --- | --- | --- | --- | --------- |
| 1   | 0   | 1   | 0   | ↑     | 1    | 1   | 0   | 1   | 0   | Load 1010 |
| 0   | 1   | 0   | 1   | ↑     | 1    | 0   | 1   | 0   | 1   | Load 0101 |
| X   | X   | X   | X   | ↑     | 0    | Q   | Q   | Q   | Q   | Hold      |
| X   | X   | X   | X   | 0/1   | X    | Q   | Q   | Q   | Q   | Hold      |

## Step-by-Step Instructions

### Step 1: Place the Flip-Flops

1. Place 4 D flip-flops on the workbench
2. Arrange them in order: FF3, FF2, FF1, FF0
3. Label them mentally as bits 3, 2, 1, 0

### Step 2: Connect Data Inputs

1. Connect D3 switch to FF3 D input
2. Connect D2 switch to FF2 D input
3. Connect D1 switch to FF1 D input
4. Connect D0 switch to FF0 D input

### Step 3: Implement Load Control

For each flip-flop, you need to implement:

```
Effective_D = (Load AND New_Data) OR (NOT Load AND Current_Q)
```

This can be done by:

1. Using AND gates to gate the data inputs with Load
2. Using OR gates to select between new data and held data
3. Or using the built-in load enable if flip-flops support it

### Step 4: Connect Clock

1. Connect Clock switch to all flip-flop clock inputs
2. All flip-flops must share the same clock signal
3. This ensures synchronous operation

### Step 5: Connect Outputs

1. Connect FF3 Q output to Q3 LED
2. Connect FF2 Q output to Q2 LED
3. Connect FF1 Q output to Q1 LED
4. Connect FF0 Q output to Q0 LED

## Testing Your Register

### Test Sequence 1: Basic Load Operation

1. Set Load = 1
2. Set data inputs: D3=1, D2=0, D1=1, D0=0 (binary 1010)
3. Apply clock pulse (0→1→0)
4. Verify outputs: Q3=1, Q2=0, Q1=1, Q0=0
5. Value 1010 should be stored

### Test Sequence 2: Hold Operation

1. Keep Load = 0
2. Change data inputs: D3=0, D2=1, D1=0, D1=1 (binary 0101)
3. Apply clock pulse
4. Verify outputs remain: Q3=1, Q2=0, Q1=1, Q0=0
5. Register should ignore new data

### Test Sequence 3: New Load Operation

1. Set Load = 1
2. Keep data inputs: D3=0, D2=1, D1=0, D0=1 (binary 0101)
3. Apply clock pulse
4. Verify outputs change to: Q3=0, Q2=1, Q1=0, Q0=1
5. New value 0101 should be stored

## Common Mistakes

### Clock Connection Issues

- **Problem**: Forgetting to connect clock to all flip-flops
- **Result**: Some bits update while others don't
- **Fix**: Ensure all flip-flops share the same clock signal

### Load Control Implementation

- **Problem**: Not implementing load enable properly
- **Result**: Register always loads or never loads
- **Fix**: Use logic gates to implement conditional loading

### Bit Ordering Confusion

- **Problem**: Connecting D0 to FF3, D3 to FF0, etc.
- **Result**: Data appears reversed or scrambled
- **Fix**: Connect D3→FF3, D2→FF2, D1→FF1, D0→FF0

## Understanding Register Behavior

### Synchronous Operation

- All bits update simultaneously on clock edge
- No race conditions between bits
- Predictable timing behavior

### Data Width

- 4-bit register stores values 0-15 (decimal)
- Each bit position has weight: 8, 4, 2, 1
- Binary 1010 = 8 + 2 = 10 decimal

### Load Enable Benefits

- **Power Saving**: Flip-flops don't switch unnecessarily
- **Data Integrity**: Prevents accidental overwrites
- **Control Logic**: Enables conditional updates

## Real-World Applications

### CPU Registers

- **Accumulator**: Stores arithmetic results
- **Index Registers**: Hold array indices
- **Address Registers**: Store memory addresses

### Data Buffering

- **Input Buffers**: Hold incoming data
- **Output Buffers**: Stage outgoing data
- **Pipeline Registers**: Store intermediate results

## Success Criteria

✅ All 4 bits load simultaneously when Load=1  
✅ Register holds value when Load=0  
✅ All flip-flops update on same clock edge  
✅ Outputs correctly reflect stored data

## Next Steps

Your 4-bit register will be used to build:

- **8-bit Register**: Double the storage capacity
- **Register Arrays**: Multiple registers for CPU
- **Memory Systems**: Addressable storage locations

The parallel storage concept you're mastering here scales up to the wide data paths found in modern processors!
