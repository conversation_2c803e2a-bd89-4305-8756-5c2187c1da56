# Build a D-Latch from NAND Gates

## Your Goal

Construct a D-Latch (Data Latch) using only NAND gates. This fundamental memory element will store one bit of information and respond to enable control.

## Key Concept

The D-Latch is a **gated memory element** that can store data when enabled and hold that data when disabled. It's the building block of all computer memory systems.

## D-Latch Specifications

### Inputs

- **D (Data)**: The bit value you want to store (0 or 1)
- **E (Enable)**: Controls when the latch can change (0 = hold, 1 = transparent)

### Output

- **Q**: The stored bit value

### Required Behavior

| E   | D   | Q(next) | Action              |
| --- | --- | ------- | ------------------- |
| 0   | X   | Q(prev) | Hold previous state |
| 1   | 0   | 0       | Store 0             |
| 1   | 1   | 1       | Store 1             |

## Construction Strategy

### Step 1: Build Upon Your SR Latch Knowledge

The D-Latch is built on the SR (Set-Reset) latch you just constructed:

- **Same memory core**: Two cross-coupled NAND gates create the memory element
- **Same feedback mechanism**: Positive feedback maintains the stored state
- **Same basic operation**: One gate sets Q=1, the other resets Q=0
- **Key improvement**: Eliminates the forbidden state problem of SR latches

### Step 2: Understand the Improvement Over SR Latch

The D-Latch solves the SR latch's forbidden state problem:

**SR Latch Issues**:

- S=0, R=0 creates forbidden state (both outputs high)
- Requires careful control to avoid this condition
- Two separate inputs (S and R) can conflict

**D-Latch Solution**:

- Single D (Data) input eliminates conflicting commands
- Enable signal provides timing control
- Input processing ensures S and R are never both 0

### Step 3: Add Enable Control

Additional NAND gates process the D and E inputs:

- When E=0: Block all changes, maintain current state
- When E=1: Allow D to control the latch state
- Input processing converts D into complementary S and R signals

### Step 3: Eliminate Forbidden States

The D input is processed to ensure:

- Set and Reset are never both active simultaneously
- Only one control signal is active at a time
- Clean transitions between states

## Circuit Architecture

You'll need **4 NAND gates** total:

### Gates 1 & 2: Memory Core (SR Latch)

- **Gate 1**: Creates the Q output
- **Gate 2**: Creates the Q̄ (complement) output
- **Cross-coupling**: Output of each gate feeds input of the other
- **Result**: Bistable memory element

### Gates 3 & 4: Input Processing

- **Gate 3**: Processes D and E to create Set signal
- **Gate 4**: Processes NOT-D and E to create Reset signal
- **Enable Control**: Both gates are controlled by E
- **Complementary Logic**: Ensures S and R are never both 0

## Construction Hints

### Hint 1: Start with the Memory Core

1. Place two NAND gates for the SR latch
2. Connect output of first NAND to input of second NAND
3. Connect output of second NAND to input of first NAND
4. This creates the basic memory element

### Hint 2: Add Input Control Gates

1. Place two more NAND gates for input processing
2. Connect their outputs to the remaining inputs of the memory core
3. These gates will process D and E signals

### Hint 3: Wire the Enable Logic

1. Connect E (Enable) to one input of both control gates
2. Connect D to one control gate
3. Connect D to the other control gate (this will create NOT-D effect)
4. The NAND gates will handle the inversion automatically

### Hint 4: Connect Inputs and Outputs

1. **D Input**: Connect to the input processing circuit
2. **E Input**: Connect to both control gates
3. **Q Output**: Take from one of the memory core gates

## Testing Your D-Latch

### Test Sequence 1: Basic Operation

1. **E=1, D=0** → Q should be 0 (transparent mode)
2. **E=1, D=1** → Q should be 1 (transparent mode)
3. **E=0, D=0** → Q should hold previous value (memory mode)
4. **E=0, D=1** → Q should still hold previous value

### Test Sequence 2: Memory Verification

1. **E=1, D=1** → Q=1 (store 1)
2. **E=0** → Q=1 (hold the 1)
3. **D=0** (while E=0) → Q=1 (still holding!)
4. **E=1** → Q=0 (now follows D again)

## Common Mistakes to Avoid

### Mistake 1: Incorrect Cross-Coupling

- **Problem**: Memory core doesn't hold state
- **Solution**: Ensure outputs properly feed back to inputs

### Mistake 2: Wrong Enable Logic

- **Problem**: Latch doesn't respond to Enable signal
- **Solution**: Both control gates must be connected to E

### Mistake 3: Missing Complementary Logic

- **Problem**: Forbidden states occur
- **Solution**: Ensure D and NOT-D are properly generated

### Mistake 4: Floating Inputs

- **Problem**: Unpredictable behavior
- **Solution**: All NAND gate inputs must be connected

## Success Criteria

Your D-Latch is working correctly when:

1. **Transparency**: Q immediately follows D when E=1
2. **Memory**: Q holds its value when E=0
3. **Clean Transitions**: No glitches or undefined states
4. **Proper Timing**: Changes occur at the right moments

## Real-World Connection

The D-Latch you're building is the same fundamental circuit used in:

- **CPU Registers**: Store data inside processors
- **Cache Memory**: High-speed temporary storage
- **RAM Cells**: Basic storage elements in memory chips
- **Pipeline Stages**: Data flow control in processors

## Debugging Tips

### If Q doesn't follow D when E=1:

- Check connections to input processing gates
- Verify E is properly connected to both control gates
- Ensure D signal reaches the input circuit

### If Q doesn't hold when E=0:

- Verify cross-coupling in the memory core
- Check that Enable properly blocks input changes
- Ensure no floating inputs on NAND gates

### If behavior is unpredictable:

- Double-check all connections
- Verify no wires are crossing incorrectly
- Test with simple input patterns first

## The Bigger Picture

Successfully building this D-Latch demonstrates:

- How memory emerges from simple logic gates
- The power of feedback in digital circuits
- Why NAND gates are called "universal"
- The foundation of all computer memory

You're literally building the same type of circuit that stores every bit of data in your computer's memory!

Ready to create your first memory element? Start with the SR latch core and build up from there!
