## Your Goal

Create addressable memory with 4 locations of 4 bits each using registers and a decoder.

## What You're Building

A **4×4 RAM** that:

- Stores 4 different 4-bit values
- Uses 2-bit addresses to select locations
- Supports both read and write operations
- Demonstrates fundamental memory architecture

## Key Concepts

### Random Access Memory (RAM)

Unlike registers that store single values, RAM provides:

- **Multiple Locations**: 4 storage locations (addresses 00, 01, 10, 11)
- **Address Selection**: Choose which location to access
- **Random Access**: Any location accessible in same time
- **Read/Write Operations**: Both store and retrieve data

### Memory Organization

```
Address | Location | Data (4 bits)
--------|----------|---------------
   00   |    0     | D3 D2 D1 D0
   01   |    1     | D3 D2 D1 D0
   10   |    2     | D3 D2 D1 D0
   11   |    3     | D3 D2 D1 D0
```

### Address Decoding

The 2-to-4 decoder converts addresses to select signals:

- **Input**: 2-bit address (A1, A0)
- **Output**: 4 select lines (one per memory location)
- **Function**: Only addressed location is selected

## Construction Strategy

### Required Components

- **4 × 4-bit Registers**: One for each memory location
- **1 × 2-to-4 Decoder**: Address decoding
- **2 Address Switches**: A1, A0
- **4 Data Switches**: D3, D2, D1, D0
- **1 Write Enable Switch**: Controls write operations
- **1 Clock Switch**: Synchronizes write operations
- **4 LEDs**: Q3, Q2, Q1, Q0 outputs

### Memory Architecture

```
Address Bus (A1, A0)
        │
    ┌───▼───┐
    │ 2:4   │ Select Lines
    │ DEC   │ ┌─────┬─────┬─────┬─────┐
    └───────┘ │     │     │     │     │
              ▼     ▼     ▼     ▼     ▼
Data Bus ──┬─Reg0──Reg1──Reg2──Reg3──┬── Data Out
           │                         │
Write ─────┼─────────────────────────┘
Clock ─────┘
```

## Step-by-Step Instructions

### Step 1: Place Memory Components

1. Place 4 × 4-bit registers (memory locations 0, 1, 2, 3)
2. Place 1 × 2-to-4 decoder
3. Arrange in logical memory layout

### Step 2: Connect Address Decoder

1. Connect A1 switch to decoder A1 input
2. Connect A0 switch to decoder A0 input
3. Connect Write Enable to decoder Enable input
4. Decoder outputs will select which register to write

### Step 3: Connect Data Inputs

1. Connect D3 switch to all registers' D3 inputs
2. Connect D2 switch to all registers' D2 inputs
3. Connect D1 switch to all registers' D1 inputs
4. Connect D0 switch to all registers' D0 inputs
5. All registers receive same data, but only selected one loads

### Step 4: Connect Write Control

1. Connect decoder Y0 output to Register 0 Load input
2. Connect decoder Y1 output to Register 1 Load input
3. Connect decoder Y2 output to Register 2 Load input
4. Connect decoder Y3 output to Register 3 Load input
5. Only addressed register will have Load=1

### Step 5: Connect Clock

1. Connect Clock switch to all registers' clock inputs
2. Write occurs on clock edge when Load=1

### Step 6: Implement Read Multiplexer

For read operations, you need to select which register's output to display:

1. Use address lines to control a 4-to-1 multiplexer
2. Connect all register outputs to multiplexer inputs
3. Multiplexer output goes to LEDs
4. Address selects which register's data appears on output

## Testing Your RAM

### Test Sequence 1: Write to Location 0

1. Set address: A1=0, A0=0 (address 00)
2. Set data: D3=1, D2=1, D1=0, D0=1 (binary 1101)
3. Set Write Enable = 1
4. Apply clock pulse
5. Data 1101 should be stored in location 0

### Test Sequence 2: Write to Location 1

1. Set address: A1=0, A0=1 (address 01)
2. Set data: D3=0, D2=1, D1=1, D0=0 (binary 0110)
3. Set Write Enable = 1
4. Apply clock pulse
5. Data 0110 should be stored in location 1

### Test Sequence 3: Read from Location 0

1. Set address: A1=0, A0=0 (address 00)
2. Set Write Enable = 0 (read mode)
3. Verify outputs show: Q3=1, Q2=1, Q1=0, Q0=1
4. Should display data stored in step 1

### Test Sequence 4: Read from Location 1

1. Set address: A1=0, A0=1 (address 01)
2. Set Write Enable = 0 (read mode)
3. Verify outputs show: Q3=0, Q2=1, Q1=1, Q0=0
4. Should display data stored in step 2

### Test Sequence 5: Verify Data Persistence

1. Write different values to all 4 locations
2. Read back each location in random order
3. Verify each location retains its stored value
4. Demonstrates independent storage

## Understanding Memory Operations

### Write Operation

1. **Set Address**: Select target location
2. **Set Data**: Place data on input lines
3. **Assert Write Enable**: Enable write operation
4. **Clock Pulse**: Store data on rising edge
5. **Result**: Data stored in addressed location

### Read Operation

1. **Set Address**: Select source location
2. **Deassert Write Enable**: Prevent accidental writes
3. **Read Data**: Data appears on output lines
4. **No Clock Needed**: Read is combinational
5. **Result**: Stored data available immediately

## Common Mistakes

### Address Decoder Connections

- **Problem**: Wrong address lines to decoder
- **Result**: Wrong locations selected
- **Fix**: A1→decoder A1, A0→decoder A0

### Write Enable Logic

- **Problem**: Not using Write Enable properly
- **Result**: Accidental writes during reads
- **Fix**: Write Enable must gate decoder enable

### Read Multiplexer

- **Problem**: Not implementing read selection
- **Result**: Can't read stored data
- **Fix**: Use address to select register output

### Clock Distribution

- **Problem**: Not connecting clock to all registers
- **Result**: Some locations don't store data
- **Fix**: Clock must reach all storage registers

## Memory Concepts

### Address Space

- **2-bit Address**: 4 possible locations (2² = 4)
- **4-bit Data**: 16 possible values per location
- **Total Capacity**: 4 × 4 = 16 bits

### Memory Timing

- **Write Setup**: Address and data stable before clock
- **Write Hold**: Address and data stable after clock
- **Read Access**: Time from address change to valid data

### Memory Hierarchy

Your 4×4 RAM demonstrates:

- **Addressability**: Each location has unique address
- **Random Access**: Any location accessible in same time
- **Volatility**: Data lost when power removed

## Real-World Applications

### Computer Memory

- **Main Memory**: Gigabytes of addressable storage
- **Cache Memory**: Fast storage near CPU
- **Register Files**: Small, fast memory in CPU

### Memory Controllers

- **Address Decoding**: Select memory chips
- **Data Routing**: Connect CPU to memory
- **Timing Control**: Coordinate read/write operations

## Success Criteria

✅ Can write different data to each of 4 locations  
✅ Can read back stored data from any location  
✅ Address correctly selects target location  
✅ Write Enable prevents accidental data corruption

## Next Steps

Your RAM understanding enables:

- **Larger Memories**: 8×8, 16×16, and beyond
- **CPU Integration**: Connect memory to processor
- **System Design**: Complete computer systems

You've built the fundamental memory architecture used in all digital computers!
