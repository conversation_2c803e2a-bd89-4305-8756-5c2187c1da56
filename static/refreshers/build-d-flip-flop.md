## Your Goal

Create an edge-triggered D flip-flop using D-latches and logic gates. Unlike a latch, a flip-flop only changes on the clock edge.

## What You're Building

A **D flip-flop** that:

- Updates output only on **rising clock edge** (0 → 1)
- Holds its value when clock is stable
- Provides both Q and Q̄ (not Q) outputs
- Forms the foundation for registers and memory

## Key Concepts

### Edge-Triggered vs. Level-Triggered

- **D-Latch (Level-Triggered)**: Output follows input when enable is HIGH
- **D Flip-Flop (Edge-Triggered)**: Output changes only when clock transitions from LOW to HIGH

### Master-Slave Design

Build using two D-latches in series:

1. **Master Latch**: Captures input when clock is LOW
2. **Slave Latch**: Transfers to output when clock goes HIGH
3. **Clock Inversion**: Master and slave use opposite clock phases

## Construction Strategy

### Required Components

- **2 D-Latches**: Master and slave stages
- **1 NOT Gate**: Invert clock for master latch
- **Switches**: For D input and Clock input
- **LEDs**: To observe Q and Q̄ outputs

### Wiring Plan

1. **Clock Inversion**:

   - Connect Clock input to NOT gate
   - NOT gate output goes to Master latch Enable

2. **Master Latch**:

   - D input → Master latch D input
   - Inverted Clock → Master latch Enable
   - Master Q output → Slave latch D input

3. **Slave Latch**:
   - Master Q → Slave D input
   - Direct Clock → Slave Enable
   - Slave Q → Final Q output
   - Slave Q̄ → Final Q̄ output

### Truth Table

| D   | Clock | Q   | Q̄   | Notes            |
| --- | ----- | --- | --- | ---------------- |
| 0   | ↑     | 0   | 1   | Rising edge, D=0 |
| 1   | ↑     | 1   | 0   | Rising edge, D=1 |
| X   | 0     | Q   | Q̄   | Clock low, hold  |
| X   | 1     | Q   | Q̄   | Clock high, hold |

## Step-by-Step Instructions

### Step 1: Set Up Clock Inversion

1. Place a NOT gate on the workbench
2. Connect Clock switch to NOT gate input
3. This creates the inverted clock signal

### Step 2: Build Master Latch

1. Place first D-latch (master)
2. Connect D input switch to master D input
3. Connect NOT gate output to master Enable
4. Master captures input when clock is LOW

### Step 3: Build Slave Latch

1. Place second D-latch (slave)
2. Connect master Q output to slave D input
3. Connect Clock switch directly to slave Enable
4. Slave transfers when clock goes HIGH

### Step 4: Connect Outputs

1. Connect slave Q output to first LED (Q output)
2. Connect slave Q̄ output to second LED (Q̄ output)
3. Verify Q and Q̄ are always opposite

## Testing Your Flip-Flop

### Test Sequence

1. **Initial State**: Set D=0, Clock=0
2. **Setup Data**: Change D to desired value
3. **Clock Pulse**: Toggle Clock 0→1→0
4. **Verify**: Output should match D value from step 2
5. **Hold Test**: Change D while Clock=0, output shouldn't change

### Expected Behavior

- **Edge Sensitivity**: Output changes only on 0→1 clock transition
- **Data Stability**: Output holds value between clock pulses
- **Complementary Outputs**: Q and Q̄ are always opposite

## Common Mistakes

### Incorrect Clock Connections

- **Problem**: Connecting clock directly to both latches
- **Result**: Flip-flop becomes transparent like a latch
- **Fix**: Use NOT gate to invert clock for master

### Missing NOT Gate

- **Problem**: Both latches enabled simultaneously
- **Result**: Input passes directly to output
- **Fix**: Add NOT gate for proper master-slave operation

### Wrong Enable Connections

- **Problem**: Swapping which latch gets inverted clock
- **Result**: Flip-flop triggers on falling edge instead
- **Fix**: Master gets inverted clock, slave gets direct clock

## Why This Matters

### Foundation for Registers

- Multiple flip-flops → Multi-bit registers
- Shared clock → Synchronous operation
- Load enable → Controlled updates

### CPU Building Block

- **Register Files**: Arrays of flip-flops
- **Pipeline Registers**: Store intermediate results
- **Control Logic**: State machines using flip-flops

### Timing Control

- **Setup Time**: Data must be stable before clock edge
- **Hold Time**: Data must remain stable after clock edge
- **Clock Skew**: Variations in clock arrival time

## Success Criteria

✅ Output changes only on rising clock edge  
✅ Output holds value when clock is stable  
✅ Q and Q̄ outputs are complementary  
✅ Input changes don't affect output until next clock edge

## Next Steps

Once you master the D flip-flop, you'll use it to build:

- **4-bit Register**: Parallel storage of multiple bits
- **8-bit Register**: Computer word-size storage
- **Memory Arrays**: Addressable storage systems

The edge-triggered behavior you're creating here is fundamental to all synchronous digital systems!
