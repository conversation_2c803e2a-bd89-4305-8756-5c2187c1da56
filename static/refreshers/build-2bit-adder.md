## Your Goal

Chain two full adders to create a 2-bit binary adder. This demonstrates how to build larger arithmetic circuits.

## What You're Building

A **2-bit Adder** that:

- Adds two 2-bit numbers (A1A0 + B1B0)
- Handles carry propagation between bit positions
- Produces a 3-bit result (Cout, Sum1, Sum0)
- Demonstrates scalable arithmetic design

## Key Concepts

### 2-bit Binary Addition

```
  A1 A0    (2-bit number A)
+ B1 B0    (2-bit number B)
+ Cin      (optional carry-in)
-------
Cout S1 S0 (3-bit result)
```

### Example Calculations

- **01 + 01 = 010**: 1 + 1 = 2 (binary 10)
- **11 + 01 = 100**: 3 + 1 = 4 (binary 100)
- **10 + 11 = 101**: 2 + 3 = 5 (binary 101)

### Carry Propagation

- Bit 0: A0 + B0 + Cin → Sum0, Carry0
- Bit 1: A1 + B1 + Carry0 → Sum1, Cout
- Carry from lower bit feeds into higher bit

## Construction Strategy

### Required Components

- **2 Full Adders**: One for each bit position
- **5 Switches**: A1, A0, B1, B0, Carry-in
- **3 LEDs**: Sum1, Sum0, Carry-out

### Bit Position Layout

```
Bit 1 (MSB):  A1 + B1 + C0 → S1, Cout
Bit 0 (LSB):  A0 + B0 + Cin → S0, C0
```

## Step-by-Step Instructions

### Step 1: Place Full Adders

1. Place first full adder (for bit 0 - LSB)
2. Place second full adder (for bit 1 - MSB)
3. Position them to show bit order clearly

### Step 2: Connect Bit 0 (LSB) Full Adder

1. Connect A0 switch to first input
2. Connect B0 switch to second input
3. Connect Carry-in switch to third input
4. Connect Sum output to Sum0 LED
5. Note the Carry-out (will connect to bit 1)

### Step 3: Connect Bit 1 (MSB) Full Adder

1. Connect A1 switch to first input
2. Connect B1 switch to second input
3. Connect Carry-out from bit 0 to third input (carry chain!)
4. Connect Sum output to Sum1 LED
5. Connect Carry-out to final Carry-out LED

### Step 4: Verify Connections

- A1, A0 → Bit 1 and Bit 0 of first number
- B1, B0 → Bit 1 and Bit 0 of second number
- Carry chain: Bit 0 Cout → Bit 1 Cin
- Outputs: Sum1, Sum0, final Carry-out

## Testing Your 2-bit Adder

### Test Case 1: Simple Addition (1 + 1)

- Inputs: A1=0, A0=1, B1=0, B0=1, Cin=0
- Expected: Sum1=1, Sum0=0, Cout=0 (binary 10 = decimal 2)

### Test Case 2: Maximum Values (3 + 1)

- Inputs: A1=1, A0=1, B1=0, B0=1, Cin=0
- Expected: Sum1=0, Sum0=0, Cout=1 (binary 100 = decimal 4)

### Test Case 3: Carry Propagation (2 + 3)

- Inputs: A1=1, A0=0, B1=1, B0=1, Cin=0
- Expected: Sum1=0, Sum0=1, Cout=1 (binary 101 = decimal 5)

### Test Case 4: With Initial Carry

- Inputs: A1=1, A0=1, B1=1, B0=1, Cin=1
- Expected: Sum1=1, Sum0=1, Cout=1 (binary 111 = decimal 7)

## Understanding Carry Propagation

### Ripple Carry Design

- Carry "ripples" from LSB to MSB
- Each bit must wait for previous bit's carry
- Simple but creates timing delays in large adders

### Timing Considerations

- Bit 0 calculates first
- Bit 1 waits for bit 0's carry
- Total delay = 2 × full adder delay

### Why This Matters

- Demonstrates how computers handle multi-bit arithmetic
- Shows trade-off between simplicity and speed
- Foundation for understanding processor design

## Common Mistakes

### Incorrect Bit Ordering

- **Problem**: Connecting A1 to bit 0, A0 to bit 1
- **Result**: Numbers appear reversed
- **Fix**: A0/B0 → LSB adder, A1/B1 → MSB adder

### Missing Carry Chain

- **Problem**: Not connecting carry from bit 0 to bit 1
- **Result**: Incorrect results for additions requiring carry
- **Fix**: Bit 0 Carry-out → Bit 1 Carry-in

### Wrong Output Interpretation

- **Problem**: Reading Sum1,Sum0 as Sum0,Sum1
- **Result**: Misinterpreting the binary result
- **Fix**: Sum1 is MSB, Sum0 is LSB

## Binary Number Representation

### 2-bit Values

| Binary | Decimal |
| ------ | ------- |
| 00     | 0       |
| 01     | 1       |
| 10     | 2       |
| 11     | 3       |

### 3-bit Results

| Binary | Decimal |
| ------ | ------- |
| 000    | 0       |
| 001    | 1       |
| 010    | 2       |
| 011    | 3       |
| 100    | 4       |
| 101    | 5       |
| 110    | 6       |
| 111    | 7       |

## Real-World Applications

### Computer Arithmetic

- **8-bit Processors**: Use 8 chained full adders
- **32-bit Processors**: Use 32 chained full adders
- **64-bit Processors**: Use 64 chained full adders

### Digital Signal Processing

- **Sample Addition**: Combine audio/video samples
- **Filtering**: Weighted sum calculations
- **Transform Operations**: FFT and similar algorithms

## Scaling to Larger Adders

### 4-bit Adder

- Chain 4 full adders
- Handle numbers 0-15
- Common in early microprocessors

### 8-bit Adder

- Chain 8 full adders
- Handle numbers 0-255
- Standard byte operations

### Modern Processors

- 64-bit adders common today
- Use advanced carry techniques
- Parallel processing for speed

## Success Criteria

✅ Correctly adds all 2-bit number combinations  
✅ Proper carry propagation from bit 0 to bit 1  
✅ Outputs correct 3-bit results  
✅ Uses two full adders connected in chain

## Next Steps

Your 2-bit adder demonstrates:

- **Scalability**: Same pattern works for any bit width
- **Modularity**: Reusing full adder building blocks
- **Architecture**: Foundation of computer arithmetic units

This design scales directly to the 8-bit, 16-bit, 32-bit, and 64-bit adders found in real processors!
