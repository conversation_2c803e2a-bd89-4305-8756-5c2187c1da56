export interface Challenge {
  id: string;
  title: string;
  description: string;
  tools: (
    | "SWITCH"
    | "NAND"
    | "LED"
    | "AND"
    | "XOR"
    | "CLOCK"
    | "D-LATCH"
    | "OR"
    | "NOT"
    | "D-FLIP-FLOP"
    | "4BIT-REGISTER"
    | "8BIT-REGISTER"
    | "2TO4-DECODER"
    | "4X4-RAM"
    | "FULL-ADDER"
    | "2BIT-ADDER"
    | "SR-LATCH"
  )[]; // Tools available for this level
  unlocks?:
    | "AND"
    | "XOR"
    | "OR"
    | "NOT"
    | "D-LATCH"
    | "D-FLIP-FLOP"
    | "4BIT-REGISTER"
    | "8BIT-REGISTER"
    | "2TO4-DECODER"
    | "4X4-RAM"
    | "FULL-ADDER"
    | "2BIT-ADDER"
    | "SR-LATCH"; // What new component this level unlocks
  verification: {
    inputs: number; // Number of SWITCHES expected
    outputs: number; // Number of LEDs expected
    truthTable: {
      inputs: (0 | 1)[];
      outputs: (0 | 1)[];
    }[];
  };
}

export interface TheorySection {
  id: string;
  title: string;
  description: string;
  content: string; // Path to the comprehensive theory content
  subsections?: TheorySubsection[];
}

export interface TheorySubsection {
  id: string;
  title: string;
  content: string; // Path to subsection content or inline markdown content
  estimatedTime?: string; // Estimated reading time for this subsection
  description?: string; // Brief description of what this subsection covers
}

// New unified learning item structure
export type LearningItemType = "theory" | "challenge" | "video" | "reading";

export interface LearningItem {
  id: string;
  type: LearningItemType;
  title: string;
  description: string;
  content?: string; // Path to content file (for theory, reading)
  challenge?: Challenge; // Challenge data (for challenge type)
  videoUrl?: string; // Video URL (for video type)
  estimatedTime?: string; // Estimated completion time (e.g., "15 min")
  subsections?: TheorySubsection[]; // Subsections for theory content
}

export interface LearningModule {
  id: string;
  title: string;
  description: string;
  introduction: string; // Brief module introduction content
  learningItems: LearningItem[]; // Unified list of all learning content
  prerequisites?: string[]; // IDs of modules that should be completed first

  // Legacy support - will be deprecated
  theory?: TheorySection; // Comprehensive theory content for this module
  challenges?: Challenge[];
}

export interface StudyContent {
  modules: LearningModule[];
  theoryIndex: TheorySection[]; // Standalone theory sections
}

// Organized curriculum by learning modules
export const studyContent: StudyContent = {
  modules: [
    {
      id: "basic-gates",
      title: "Basic Logic Gates",
      description:
        "Build the fundamental components of your 8-bit computer: AND, OR, NOT, and XOR gates from NAND gates.",
      introduction:
        "Welcome to the foundation of digital logic! In this module, you'll learn how all digital circuits are built from simple logic gates, and how you can construct any gate using just NAND gates.",
      learningItems: [
        {
          id: "basic-gates-overview",
          type: "theory",
          title: "Introduction to Logic Gates",
          description:
            "An overview of the fundamental building blocks of digital circuits.",
          estimatedTime: "15 min",
          subsections: [
            {
              id: "what-are-logic-gates",
              title: "What Are Logic Gates?",
              description: "Understanding the basic concept and purpose of logic gates in digital systems.",
              content: "/theory/basic-gates/basic-gates-overview/what-are-logic-gates.md",
              estimatedTime: "3 min",
            },
            {
              id: "types-of-logic-gates",
              title: "Basic Types of Logic Gates",
              description: "Learn about the most common logic gates and their truth tables.",
              content: "/theory/basic-gates/basic-gates-overview/types-of-logic-gates.md",
              estimatedTime: "4 min",
            },
            {
              id: "building-complex-circuits",
              title: "Building Complex Circuits",
              description: "Discover how simple gates combine to create complex digital systems.",
              content: "/theory/basic-gates/basic-gates-overview/building-complex-circuits.md",
              estimatedTime: "3 min",
            },
          ],
        },
        {
          id: "universal-gates-theory",
          type: "theory",
          title: "Universal Gates & NAND Logic",
          description:
            'Learn why NAND is called a "universal gate" and how it forms the foundation of all digital logic.',
          estimatedTime: "10 min",
          subsections: [
            {
              id: "what-is-nand",
              title: "Understanding NAND Gates",
              description: "Learn what NAND gates are and how they work.",
              content: "/theory/basic-gates/universal-gates-theory/what-is-nand.md",
              estimatedTime: "3 min",
            },
            {
              id: "universal-gate-concept",
              title: "The Universal Gate Concept",
              description: "Discover why NAND is called a 'universal gate' and what that means.",
              content: "/theory/basic-gates/universal-gates-theory/universal-gate-concept.md",
              estimatedTime: "3 min",
            },
            {
              id: "building-gates-from-nand",
              title: "Building Other Gates from NAND",
              description: "See practical examples of how to construct AND, OR, and NOT gates using only NAND gates.",
              content: "/theory/basic-gates/universal-gates-theory/building-gates-from-nand.md",
              estimatedTime: "4 min",
            },
          ],
        },
        {
          id: "and-gate-construction-theory",
          type: "theory",
          title: "AND Gate Construction Guide",
          description:
            "Learn how to build an AND gate using NAND gates with step-by-step instructions.",
          content: "/refreshers/build-and.md",
          estimatedTime: "8 min",
        },
        {
          id: "build-and-gate",
          type: "challenge",
          title: "Build an AND Gate",
          description: "Construct an AND gate using only NAND gates.",
          estimatedTime: "15 min",
          challenge: {
            id: "build-and",
            title: "Build an AND Gate",
            description:
              "Use NAND gates to create an AND gate. Remember: AND is the opposite of NAND!",
            tools: ["SWITCH", "NAND", "LED"],
            unlocks: "AND",
            verification: {
              inputs: 2,
              outputs: 1,
              truthTable: [
                { inputs: [0, 0], outputs: [0] },
                { inputs: [0, 1], outputs: [0] },
                { inputs: [1, 0], outputs: [0] },
                { inputs: [1, 1], outputs: [1] },
              ],
            },
          },
        },
        {
          id: "boolean-algebra-theory",
          type: "theory",
          title: "Boolean Algebra Fundamentals",
          description:
            "Master the mathematical foundation behind logic gates and digital circuits.",
          estimatedTime: "12 min",
          subsections: [
            {
              id: "introduction-to-boolean-algebra",
              title: "What is Boolean Algebra?",
              description: "Learn the mathematical foundation that powers all digital logic.",
              content: "/theory/basic-gates/introduction-to-boolean-algebra.md",
              estimatedTime: "3 min",
            },
            {
              id: "boolean-operations",
              title: "Basic Boolean Operations",
              description: "Master the fundamental operations: AND, OR, and NOT.",
              content: "/theory/basic-gates/boolean-operations.md",
              estimatedTime: "4 min",
            },
            {
              id: "boolean-laws",
              title: "Boolean Laws and Simplification",
              description: "Learn the key laws that help simplify complex Boolean expressions.",
              content: "/theory/basic-gates/boolean-laws.md",
              estimatedTime: "5 min",
            },
          ],
        },
        {
          id: "or-gate-construction-theory",
          type: "theory",
          title: "OR Gate Construction Guide",
          description:
            "Master De Morgan's laws and learn how to build an OR gate using NAND gates.",
          content: "/refreshers/build-or.md",
          estimatedTime: "12 min",
        },
        {
          id: "build-or-gate",
          type: "challenge",
          title: "Build an OR Gate",
          description:
            "Use NAND gates to construct an OR gate. An OR gate outputs 1 when at least one of its inputs is 1.",
          estimatedTime: "15 min",
          challenge: {
            id: "build-or",
            title: "Build an OR Gate",
            description:
              "Use NAND gates to construct an OR gate. An OR gate outputs 1 when at least one of its inputs is 1.",
            tools: ["SWITCH", "NAND", "LED"],
            unlocks: "OR",
            verification: {
              inputs: 2,
              outputs: 1,
              truthTable: [
                { inputs: [0, 0], outputs: [0] },
                { inputs: [0, 1], outputs: [1] },
                { inputs: [1, 0], outputs: [1] },
                { inputs: [1, 1], outputs: [1] },
              ],
            },
          },
        },
        {
          id: "not-gate-construction-theory",
          type: "theory",
          title: "NOT Gate Construction Guide",
          description:
            "Learn the simplest gate construction - building a NOT gate (inverter) from a NAND gate.",
          content: "/refreshers/build-not.md",
          estimatedTime: "5 min",
        },
        {
          id: "build-not-gate",
          type: "challenge",
          title: "Build a NOT Gate",
          description:
            "Create a NOT gate (inverter) using a single NAND gate. A NOT gate outputs the opposite of its input.",
          estimatedTime: "10 min",
          challenge: {
            id: "build-not",
            title: "Build a NOT Gate",
            description:
              "Create a NOT gate (inverter) using a single NAND gate. A NOT gate outputs the opposite of its input.",
            tools: ["SWITCH", "NAND", "LED"],
            unlocks: "NOT",
            verification: {
              inputs: 1,
              outputs: 1,
              truthTable: [
                { inputs: [0], outputs: [1] },
                { inputs: [1], outputs: [0] },
              ],
            },
          },
        },
        {
          id: "gate-construction-theory",
          type: "theory",
          title: "Advanced Gate Construction",
          description:
            "Learn advanced techniques for building complex gates and optimizing your circuits.",
          estimatedTime: "8 min",
          subsections: [
            {
              id: "optimization-principles",
              title: "Circuit Optimization Principles",
              description: "Learn how to minimize gate count, reduce delay, and manage fan-out for efficient circuits.",
              content: "/theory/basic-gates/gate-construction-theory/optimization-principles.md",
              estimatedTime: "3 min",
            },
            {
              id: "multi-input-gates",
              title: "Multi-Input Gate Construction",
              description: "Build gates with 3, 4, or more inputs using tree structures and efficient designs.",
              content: "/theory/basic-gates/gate-construction-theory/multi-input-gates.md",
              estimatedTime: "3 min",
            },
            {
              id: "compound-logic-functions",
              title: "Compound Logic Functions",
              description: "Master XOR, XNOR, multiplexers, and other complex logic functions.",
              content: "/theory/basic-gates/gate-construction-theory/compound-logic-functions.md",
              estimatedTime: "2 min",
            },
          ],
        },
        {
          id: "xor-gate-construction-theory",
          type: "theory",
          title: "XOR Gate Construction Guide",
          description:
            "Learn how to build the complex XOR (Exclusive OR) gate using the gates you've already constructed.",
          estimatedTime: "10 min",
          subsections: [
            {
              id: "understanding-xor",
              title: "Understanding XOR Gates",
              description: "Learn what XOR gates are, how they work, and why they're essential for computer arithmetic.",
              content: "/theory/basic-gates/xor-construction-theory/understanding-xor.md",
              estimatedTime: "3 min",
            },
            {
              id: "construction-strategy",
              title: "XOR Construction Strategy",
              description: "Master the step-by-step process for building XOR gates from NAND gates efficiently.",
              content: "/theory/basic-gates/xor-construction-theory/construction-strategy.md",
              estimatedTime: "4 min",
            },
            {
              id: "testing-and-applications",
              title: "Testing XOR and Applications",
              description: "Learn how to test your XOR gate and discover its real-world applications in computing.",
              content: "/theory/basic-gates/xor-construction-theory/testing-and-applications.md",
              estimatedTime: "3 min",
            },
          ],
        },
        {
          id: "build-xor-gate",
          type: "challenge",
          title: "Build an XOR Gate",
          description:
            "Use NAND gates to build an XOR (Exclusive OR) gate. It should output 1 only when its inputs are different.",
          estimatedTime: "20 min",
          challenge: {
            id: "build-xor",
            title: "Build an XOR Gate",
            description:
              "Use NAND gates to build an XOR (Exclusive OR) gate. It should output 1 only when its inputs are different.",
            tools: ["SWITCH", "NAND", "LED"],
            unlocks: "XOR",
            verification: {
              inputs: 2,
              outputs: 1,
              truthTable: [
                { inputs: [0, 0], outputs: [0] },
                { inputs: [0, 1], outputs: [1] },
                { inputs: [1, 0], outputs: [1] },
                { inputs: [1, 1], outputs: [0] },
              ],
            },
          },
        },
      ],
      // Legacy support
      theory: {
        id: "basic-gates-theory",
        title: "Basic Logic Gates Theory",
        description: "Comprehensive guide to understanding logic gates",
        content: "/theory/basic-gates.md",
        subsections: [
          {
            id: "universal-gates",
            title: "Universal Gates",
            content: "/theory/basic-gates/universal-gates.md",
          },
          {
            id: "gate-construction",
            title: "Gate Construction",
            content: "/theory/basic-gates/construction.md",
          },
        ],
      },
      challenges: [
        {
          id: "build-and",
          title: "Build an AND Gate",
          description:
            "Use the provided NAND gates to construct a functional AND gate. An AND gate should only output 1 when both of its inputs are 1.",
          tools: ["SWITCH", "NAND", "LED"],
          unlocks: "AND",
          verification: {
            inputs: 2,
            outputs: 1,
            truthTable: [
              { inputs: [0, 0], outputs: [0] },
              { inputs: [0, 1], outputs: [0] },
              { inputs: [1, 0], outputs: [0] },
              { inputs: [1, 1], outputs: [1] },
            ],
          },
        },
        {
          id: "build-xor",
          title: "Build an XOR Gate",
          description:
            "Use NAND gates to build an XOR (Exclusive OR) gate. It should output 1 only when its inputs are different.",
          tools: ["SWITCH", "NAND", "LED"],
          unlocks: "XOR",
          verification: {
            inputs: 2,
            outputs: 1,
            truthTable: [
              { inputs: [0, 0], outputs: [0] },
              { inputs: [0, 1], outputs: [1] },
              { inputs: [1, 0], outputs: [1] },
              { inputs: [1, 1], outputs: [0] },
            ],
          },
        },
        {
          id: "build-or",
          title: "Build an OR Gate",
          description:
            "Use NAND gates to construct an OR gate. An OR gate outputs 1 when at least one of its inputs is 1.",
          tools: ["SWITCH", "NAND", "LED"],
          unlocks: "OR",
          verification: {
            inputs: 2,
            outputs: 1,
            truthTable: [
              { inputs: [0, 0], outputs: [0] },
              { inputs: [0, 1], outputs: [1] },
              { inputs: [1, 0], outputs: [1] },
              { inputs: [1, 1], outputs: [1] },
            ],
          },
        },
        {
          id: "build-not",
          title: "Build a NOT Gate",
          description:
            "Create a NOT gate (inverter) using a single NAND gate. A NOT gate outputs the opposite of its input.",
          tools: ["SWITCH", "NAND", "LED"],
          unlocks: "NOT",
          verification: {
            inputs: 1,
            outputs: 1,
            truthTable: [
              { inputs: [0], outputs: [1] },
              { inputs: [1], outputs: [0] },
            ],
          },
        },
      ],
    },
    {
      id: "combinational-logic",
      title: "Combinational Logic",
      description:
        "Construct the arithmetic and logic unit (ALU) components for your 8-bit computer using combinational circuits.",
      introduction:
        "Now that you've mastered basic gates, let's build more complex circuits! Combinational logic circuits produce outputs that depend only on current inputs - no memory involved.",
      learningItems: [
        {
          id: "arithmetic-circuits-theory",
          type: "theory",
          title: "Introduction to Arithmetic Circuits",
          description:
            "Learn how computers perform mathematical operations using logic gates.",
          content: "/theory/combinational-logic/arithmetic.md",
          estimatedTime: "12 min",
        },
        {
          id: "half-adder-construction-theory",
          type: "theory",
          title: "Half-Adder Construction Guide",
          description:
            "Learn how to build a Half-Adder circuit that adds two bits and produces Sum and Carry outputs.",
          content: "/refreshers/build-half-adder.md",
          estimatedTime: "10 min",
        },
        {
          id: "build-half-adder",
          type: "challenge",
          title: "Build a Half-Adder",
          description:
            "A Half-Adder adds two bits. It has two inputs (A, B) and two outputs (Sum, Carry).",
          estimatedTime: "20 min",
          challenge: {
            id: "build-half-adder",
            title: "Build a Half-Adder",
            description:
              "A Half-Adder adds two bits. It has two inputs (A, B) and two outputs (Sum, Carry). Use the AND and XOR gates you have unlocked.",
            tools: ["SWITCH", "LED", "AND", "XOR", "NAND"],
            unlocks: undefined,
            verification: {
              inputs: 2,
              outputs: 2,
              truthTable: [
                { inputs: [0, 0], outputs: [0, 0] },
                { inputs: [0, 1], outputs: [1, 0] },
                { inputs: [1, 0], outputs: [1, 0] },
                { inputs: [1, 1], outputs: [0, 1] },
              ],
            },
          },
        },
        {
          id: "design-principles-theory",
          type: "theory",
          title: "Circuit Design Principles",
          description:
            "Master the principles of designing efficient and reliable digital circuits.",
          content: "/theory/combinational-logic/design.md",
          estimatedTime: "10 min",
        },
        {
          id: "full-adder-construction-theory",
          type: "theory",
          title: "Full Adder Construction Guide",
          description:
            "Learn how to build a Full Adder that handles carry-in and produces both sum and carry-out.",
          content: "/refreshers/build-full-adder.md",
          estimatedTime: "12 min",
        },
        {
          id: "build-full-adder",
          type: "challenge",
          title: "Build a Full Adder",
          description:
            "Create a full adder that adds three bits (A, B, Carry-in) and produces Sum and Carry-out.",
          estimatedTime: "25 min",
          challenge: {
            id: "build-full-adder",
            title: "Build a Full Adder",
            description:
              "Create a full adder that adds three bits (A, B, Carry-in) and produces Sum and Carry-out. Use the gates you've unlocked.",
            tools: ["SWITCH", "LED", "AND", "XOR", "OR"],
            unlocks: "FULL-ADDER",
            verification: {
              inputs: 3, // A, B, Carry-in
              outputs: 2, // Sum, Carry-out
              truthTable: [
                { inputs: [0, 0, 0], outputs: [0, 0] },
                { inputs: [0, 0, 1], outputs: [1, 0] },
                { inputs: [0, 1, 0], outputs: [1, 0] },
                { inputs: [0, 1, 1], outputs: [0, 1] },
                { inputs: [1, 0, 0], outputs: [1, 0] },
                { inputs: [1, 0, 1], outputs: [0, 1] },
                { inputs: [1, 1, 0], outputs: [0, 1] },
                { inputs: [1, 1, 1], outputs: [1, 1] },
              ],
            },
          },
        },
        {
          id: "2bit-adder-construction-theory",
          type: "theory",
          title: "2-bit Adder Construction Guide",
          description:
            "Learn how to chain full adders together to create multi-bit arithmetic circuits.",
          content: "/refreshers/build-2bit-adder.md",
          estimatedTime: "15 min",
        },
        {
          id: "build-2bit-adder",
          type: "challenge",
          title: "Build a 2-bit Adder",
          description: "Chain two full adders to create a 2-bit binary adder.",
          estimatedTime: "30 min",
          challenge: {
            id: "build-2bit-adder",
            title: "Build a 2-bit Adder",
            description:
              "Chain two full adders to create a 2-bit binary adder. This demonstrates how to build larger arithmetic circuits.",
            tools: ["SWITCH", "LED", "FULL-ADDER"],
            unlocks: "2BIT-ADDER",
            verification: {
              inputs: 5, // A1, A0, B1, B0, Carry-in
              outputs: 3, // Sum1, Sum0, Carry-out
              truthTable: [
                { inputs: [0, 1, 0, 1, 0], outputs: [1, 0, 0] }, // 1 + 1 = 10
                { inputs: [1, 1, 0, 1, 0], outputs: [0, 0, 1] }, // 11 + 01 = 100
                { inputs: [1, 0, 1, 1, 0], outputs: [0, 1, 1] }, // 10 + 11 = 101
              ],
            },
          },
        },
      ],
      prerequisites: ["basic-gates"],
      // Legacy support
      theory: {
        id: "combinational-logic-theory",
        title: "Combinational Logic Theory",
        description: "Understanding circuits without memory",
        content: "/theory/combinational-logic.md",
        subsections: [
          {
            id: "arithmetic-circuits",
            title: "Arithmetic Circuits",
            content: "/theory/combinational-logic/arithmetic.md",
          },
          {
            id: "design-principles",
            title: "Design Principles",
            content: "/theory/combinational-logic/design.md",
          },
        ],
      },
      challenges: [
        {
          id: "build-half-adder",
          title: "Build a Half-Adder",
          description:
            "A Half-Adder adds two bits. It has two inputs (A, B) and two outputs (Sum, Carry). Use the AND and XOR gates you have unlocked.",
          tools: ["SWITCH", "LED", "AND", "XOR", "NAND"],
          unlocks: undefined,
          verification: {
            inputs: 2,
            outputs: 2,
            truthTable: [
              { inputs: [0, 0], outputs: [0, 0] },
              { inputs: [0, 1], outputs: [1, 0] },
              { inputs: [1, 0], outputs: [1, 0] },
              { inputs: [1, 1], outputs: [0, 1] },
            ],
          },
        },
        {
          id: "build-full-adder",
          title: "Build a Full Adder",
          description:
            "Create a full adder that adds three bits (A, B, Carry-in) and produces Sum and Carry-out. Use the gates you've unlocked.",
          tools: ["SWITCH", "LED", "AND", "XOR", "OR"],
          unlocks: "FULL-ADDER",
          verification: {
            inputs: 3, // A, B, Carry-in
            outputs: 2, // Sum, Carry-out
            truthTable: [
              { inputs: [0, 0, 0], outputs: [0, 0] },
              { inputs: [0, 0, 1], outputs: [1, 0] },
              { inputs: [0, 1, 0], outputs: [1, 0] },
              { inputs: [0, 1, 1], outputs: [0, 1] },
              { inputs: [1, 0, 0], outputs: [1, 0] },
              { inputs: [1, 0, 1], outputs: [0, 1] },
              { inputs: [1, 1, 0], outputs: [0, 1] },
              { inputs: [1, 1, 1], outputs: [1, 1] },
            ],
          },
        },
        {
          id: "build-2bit-adder",
          title: "Build a 2-bit Adder",
          description:
            "Chain two full adders to create a 2-bit binary adder. This demonstrates how to build larger arithmetic circuits.",
          tools: ["SWITCH", "LED", "FULL-ADDER"],
          unlocks: "2BIT-ADDER",
          verification: {
            inputs: 5, // A1, A0, B1, B0, Carry-in
            outputs: 3, // Sum1, Sum0, Carry-out
            truthTable: [
              { inputs: [0, 1, 0, 1, 0], outputs: [1, 0, 0] }, // 1 + 1 = 10
              { inputs: [1, 1, 0, 1, 0], outputs: [0, 0, 1] }, // 11 + 01 = 100
              { inputs: [1, 0, 1, 1, 0], outputs: [0, 1, 1] }, // 10 + 11 = 101
            ],
          },
        },
      ],
    },
    {
      id: "sequential-logic",
      title: "Sequential Logic",
      description:
        "Build memory elements and control circuits that give your 8-bit computer the ability to store state and execute programs.",
      introduction:
        "Welcome to the world of memory! Sequential logic circuits can remember previous states, enabling your computer to store information and execute programs step by step.",
      learningItems: [
        {
          id: "memory-elements-theory",
          type: "theory",
          title: "Introduction to Memory Elements",
          description:
            "Discover how digital circuits can store and remember information.",
          content: "/theory/sequential-logic/memory-elements.md",
          estimatedTime: "15 min",
        },
        {
          id: "sr-latch-construction-theory",
          type: "theory",
          title: "SR Latch Construction Guide",
          description:
            "Learn how to build an SR (Set-Reset) latch using NAND gates - the foundation of all memory.",
          content: "/refreshers/build-sr-latch.md",
          estimatedTime: "15 min",
        },
        {
          id: "build-sr-latch",
          type: "challenge",
          title: "Build an SR Latch",
          description:
            "Create an SR (Set-Reset) latch using NAND gates - the foundation of all memory elements.",
          estimatedTime: "25 min",
          challenge: {
            id: "build-sr-latch",
            title: "Build an SR Latch",
            description:
              "Create an SR (Set-Reset) latch using NAND gates. This is the foundation of all memory elements.",
            tools: ["SWITCH", "LED", "NAND"],
            unlocks: "SR-LATCH",
            verification: {
              inputs: 2, // S, R
              outputs: 2, // Q, Q̄
              truthTable: [
                { inputs: [1, 1], outputs: [0, 1] }, // Hold state (previous reset)
                { inputs: [1, 0], outputs: [0, 1] }, // Reset (Q=0, Q̄=1)
                { inputs: [0, 1], outputs: [1, 0] }, // Set (Q=1, Q̄=0)
                { inputs: [0, 0], outputs: [1, 1] }, // Forbidden state (both outputs high)
              ],
            },
          },
        },
        {
          id: "d-latch-construction-theory",
          type: "theory",
          title: "D-Latch Construction from NAND Gates",
          description:
            "Learn how to build a D-Latch that improves upon the SR Latch by eliminating forbidden states.",
          content: "/theory/sequential-logic/d-latch-construction.md",
          estimatedTime: "18 min",
        },
        {
          id: "build-d-latch",
          type: "challenge",
          title: "Build a D-Latch",
          description:
            "Build a D-Latch that improves upon the SR Latch design by eliminating forbidden states.",
          estimatedTime: "30 min",
          challenge: {
            id: "build-d-latch",
            title: "Build a D-Latch",
            description:
              "Build a D-Latch that improves upon your SR Latch by adding enable control and eliminating forbidden states. Use only NAND gates.",
            tools: ["SWITCH", "NAND", "LED"],
            unlocks: "D-LATCH",
            verification: {
              inputs: 2, // D, E
              outputs: 1, // Q
              truthTable: [
                { inputs: [0, 0], outputs: [0] }, // Hold state (assuming previous was 0)
                { inputs: [1, 0], outputs: [0] }, // Hold state (E=0, so ignore D)
                { inputs: [0, 1], outputs: [0] }, // Transparent mode: D=0 → Q=0
                { inputs: [1, 1], outputs: [1] }, // Transparent mode: D=1 → Q=1
              ],
            },
          },
        },
        {
          id: "observe-d-latch",
          type: "challenge",
          title: "The Gift of Memory (D Latch)",
          description:
            "Explore how a D Latch can store information and understand the basics of memory.",
          estimatedTime: "15 min",
          challenge: {
            id: "observe-d-latch",
            title: "The Gift of Memory (D Latch)",
            description:
              'This is a D Latch, the basic unit of memory. Connect the CLOCK to the "E" (Enable) pin and a SWITCH to the "D" (Data) pin. Observe how the output "Q" only updates when the clock is high (1). This is memory!',
            tools: ["SWITCH", "LED", "CLOCK", "D-LATCH"],
            unlocks: undefined,
            verification: {
              inputs: 0,
              outputs: 0,
              truthTable: [],
            },
          },
        },
        {
          id: "timing-concepts-theory",
          type: "theory",
          title: "Timing and Clocking",
          description:
            "Master the critical concepts of timing and synchronization in digital systems.",
          content: "/theory/sequential-logic/timing.md",
          estimatedTime: "12 min",
        },
      ],
      prerequisites: ["combinational-logic"],
      // Legacy support
      theory: {
        id: "sequential-logic-theory",
        title: "Sequential Logic Theory",
        description: "Understanding memory and state in digital circuits",
        content: "/theory/sequential-logic.md",
        subsections: [
          {
            id: "memory-elements",
            title: "Memory Elements",
            content: "/theory/sequential-logic/memory-elements.md",
          },
          {
            id: "timing-concepts",
            title: "Timing and Clocking",
            content: "/theory/sequential-logic/timing.md",
          },
        ],
      },
      challenges: [
        {
          id: "observe-d-latch",
          title: "The Gift of Memory (D Latch)",
          description:
            'This is a D Latch, the basic unit of memory. Connect the CLOCK to the "E" (Enable) pin and a SWITCH to the "D" (Data) pin. Observe how the output "Q" only updates when the clock is high (1). This is memory!',
          tools: ["SWITCH", "LED", "CLOCK", "D-LATCH"],
          unlocks: undefined,
          verification: {
            inputs: 0,
            outputs: 0,
            truthTable: [],
          },
        },
        {
          id: "build-sr-latch",
          title: "Build an SR Latch",
          description:
            "Create an SR (Set-Reset) latch using NAND gates. This is the foundation of all memory elements.",
          tools: ["SWITCH", "LED", "NAND"],
          unlocks: "SR-LATCH",
          verification: {
            inputs: 2, // S, R
            outputs: 2, // Q, Q̄
            truthTable: [
              { inputs: [1, 1], outputs: [0, 1] }, // Hold state (previous reset)
              { inputs: [1, 0], outputs: [0, 1] }, // Reset (Q=0, Q̄=1)
              { inputs: [0, 1], outputs: [1, 0] }, // Set (Q=1, Q̄=0)
              { inputs: [0, 0], outputs: [1, 1] }, // Forbidden state (both outputs high)
            ],
          },
        },
      ],
    },
    {
      id: "registers",
      title: "Registers & Memory",
      description:
        "Create the memory system for your 8-bit computer, including registers, RAM, and the complete memory hierarchy.",
      introduction:
        "Time to build the memory system! You'll create multi-bit storage units and addressable memory that form the foundation of any computer's data storage capabilities.",
      learningItems: [
        {
          id: "register-types-theory",
          type: "theory",
          title: "Types of Registers",
          description:
            "Learn about different types of registers and their applications in computer systems.",
          content: "/theory/registers/types.md",
          estimatedTime: "10 min",
        },
        {
          id: "d-flip-flop-construction-theory",
          type: "theory",
          title: "D Flip-Flop Construction Guide",
          description:
            "Learn how to build an edge-triggered D flip-flop using D-latches and understand edge triggering.",
          content: "/refreshers/build-d-flip-flop.md",
          estimatedTime: "12 min",
        },
        {
          id: "build-d-flip-flop",
          type: "challenge",
          title: "Build a D Flip-Flop",
          description:
            "Create an edge-triggered D flip-flop using D-latches and logic gates.",
          estimatedTime: "25 min",
          challenge: {
            id: "build-d-flip-flop",
            title: "Build a D Flip-Flop",
            description:
              "Create an edge-triggered D flip-flop using D-latches and logic gates. Unlike a latch, a flip-flop only changes on the clock edge.",
            tools: ["SWITCH", "LED", "CLOCK", "D-LATCH", "NOT"],
            unlocks: "D-FLIP-FLOP",
            verification: {
              inputs: 2, // D, Clock
              outputs: 2, // Q, Q̄
              truthTable: [
                { inputs: [0, 0], outputs: [0, 1] }, // Initial state
                { inputs: [1, 1], outputs: [1, 0] }, // Clock high, D=1
                { inputs: [0, 1], outputs: [1, 0] }, // D changes but clock still high
                { inputs: [0, 0], outputs: [1, 0] }, // Clock low, output holds
              ],
            },
          },
        },
        {
          id: "4bit-register-construction-theory",
          type: "theory",
          title: "4-bit Register Construction Guide",
          description:
            "Learn how to combine D flip-flops to create multi-bit storage registers with load control.",
          content: "/refreshers/build-4bit-register.md",
          estimatedTime: "15 min",
        },
        {
          id: "build-4bit-register",
          type: "challenge",
          title: "Build a 4-bit Register",
          description:
            "Combine 4 D flip-flops to create a 4-bit storage register with load enable control.",
          estimatedTime: "30 min",
          challenge: {
            id: "build-4bit-register",
            title: "Build a 4-bit Register",
            description:
              "Combine 4 D flip-flops to create a 4-bit storage register with load enable control.",
            tools: ["SWITCH", "LED", "CLOCK", "D-FLIP-FLOP"],
            unlocks: "4BIT-REGISTER",
            verification: {
              inputs: 6, // D3-D0, Clock, Load
              outputs: 4, // Q3-Q0
              truthTable: [
                { inputs: [1, 0, 1, 0, 1, 1], outputs: [1, 0, 1, 0] }, // Load 1010
                { inputs: [0, 1, 0, 1, 1, 0], outputs: [1, 0, 1, 0] }, // Load disabled, holds
                { inputs: [0, 1, 0, 1, 1, 1], outputs: [0, 1, 0, 1] }, // Load 0101
              ],
            },
          },
        },
        {
          id: "memory-hierarchy-theory",
          type: "theory",
          title: "Memory Hierarchy",
          description:
            "Understand how different types of memory work together in computer systems.",
          content: "/theory/registers/hierarchy.md",
          estimatedTime: "12 min",
        },
        {
          id: "8bit-register-construction-theory",
          type: "theory",
          title: "8-bit Register Construction Guide",
          description:
            "Learn how to expand to 8-bit storage by combining 4-bit register components.",
          content: "/refreshers/build-8bit-register.md",
          estimatedTime: "10 min",
        },
        {
          id: "build-8bit-register",
          type: "challenge",
          title: "Build an 8-bit Register",
          description:
            "Expand to an 8-bit register using your 4-bit register components.",
          estimatedTime: "25 min",
          challenge: {
            id: "build-8bit-register",
            title: "Build an 8-bit Register",
            description:
              "Expand to an 8-bit register using your 4-bit register components or individual flip-flops.",
            tools: ["SWITCH", "LED", "CLOCK", "4BIT-REGISTER"],
            unlocks: "8BIT-REGISTER",
            verification: {
              inputs: 10, // D7-D0, Clock, Load
              outputs: 8, // Q7-Q0
              truthTable: [
                {
                  inputs: [1, 0, 1, 0, 1, 0, 1, 0, 1, 1],
                  outputs: [1, 0, 1, 0, 1, 0, 1, 0],
                },
                {
                  inputs: [0, 1, 0, 1, 0, 1, 0, 1, 1, 1],
                  outputs: [0, 1, 0, 1, 0, 1, 0, 1],
                },
              ],
            },
          },
        },
        {
          id: "4x4-ram-construction-theory",
          type: "theory",
          title: "4×4 RAM Construction Guide",
          description:
            "Learn how to build addressable memory using registers and decoders to create simple RAM.",
          content: "/refreshers/build-4x4-ram.md",
          estimatedTime: "20 min",
        },
        {
          id: "build-4x4-ram",
          type: "challenge",
          title: "Build Simple Memory (4×4 RAM)",
          description:
            "Create addressable memory with 4 locations of 4 bits each using registers and a decoder.",
          estimatedTime: "35 min",
          challenge: {
            id: "build-4x4-ram",
            title: "Build Simple Memory (4×4 RAM)",
            description:
              "Create addressable memory with 4 locations of 4 bits each using registers and a decoder.",
            tools: ["SWITCH", "LED", "CLOCK", "4BIT-REGISTER", "2TO4-DECODER"],
            unlocks: "4X4-RAM",
            verification: {
              inputs: 8, // A1-A0, D3-D0, Write-Enable, Clock
              outputs: 4, // Q3-Q0
              truthTable: [
                // Write to address 0
                { inputs: [0, 0, 1, 1, 0, 1, 1, 1], outputs: [1, 1, 0, 1] },
                // Read from address 0
                { inputs: [0, 0, 0, 0, 0, 0, 0, 0], outputs: [1, 1, 0, 1] },
                // Write to address 1
                { inputs: [0, 1, 0, 1, 1, 0, 1, 1], outputs: [0, 1, 1, 0] },
              ],
            },
          },
        },
      ],
      prerequisites: ["sequential-logic"],
      // Legacy support
      theory: {
        id: "registers-theory",
        title: "Registers & Memory Theory",
        description: "Multi-bit storage and memory systems",
        content: "/theory/registers.md",
        subsections: [
          {
            id: "register-types",
            title: "Types of Registers",
            content: "/theory/registers/types.md",
          },
          {
            id: "memory-hierarchy",
            title: "Memory Hierarchy",
            content: "/theory/registers/hierarchy.md",
          },
        ],
      },
      challenges: [
        {
          id: "build-d-flip-flop",
          title: "Build a D Flip-Flop",
          description:
            "Create an edge-triggered D flip-flop using D-latches and logic gates. Unlike a latch, a flip-flop only changes on the clock edge.",
          tools: ["SWITCH", "LED", "CLOCK", "D-LATCH", "NOT"],
          unlocks: "D-FLIP-FLOP",
          verification: {
            inputs: 2, // D, Clock
            outputs: 2, // Q, Q̄
            truthTable: [
              { inputs: [0, 0], outputs: [0, 1] }, // Initial state
              { inputs: [1, 1], outputs: [1, 0] }, // Clock high, D=1
              { inputs: [0, 1], outputs: [1, 0] }, // D changes but clock still high
              { inputs: [0, 0], outputs: [1, 0] }, // Clock low, output holds
            ],
          },
        },
        {
          id: "build-4bit-register",
          title: "Build a 4-bit Register",
          description:
            "Combine 4 D flip-flops to create a 4-bit storage register with load enable control.",
          tools: ["SWITCH", "LED", "CLOCK", "D-FLIP-FLOP"],
          unlocks: "4BIT-REGISTER",
          verification: {
            inputs: 6, // D3-D0, Clock, Load
            outputs: 4, // Q3-Q0
            truthTable: [
              { inputs: [1, 0, 1, 0, 1, 1], outputs: [1, 0, 1, 0] }, // Load 1010
              { inputs: [0, 1, 0, 1, 1, 0], outputs: [1, 0, 1, 0] }, // Load disabled, holds
              { inputs: [0, 1, 0, 1, 1, 1], outputs: [0, 1, 0, 1] }, // Load 0101
            ],
          },
        },
        {
          id: "build-8bit-register",
          title: "Build an 8-bit Register",
          description:
            "Expand to an 8-bit register using your 4-bit register components or individual flip-flops.",
          tools: ["SWITCH", "LED", "CLOCK", "4BIT-REGISTER"],
          unlocks: "8BIT-REGISTER",
          verification: {
            inputs: 10, // D7-D0, Clock, Load
            outputs: 8, // Q7-Q0
            truthTable: [
              {
                inputs: [1, 0, 1, 0, 1, 0, 1, 0, 1, 1],
                outputs: [1, 0, 1, 0, 1, 0, 1, 0],
              },
              {
                inputs: [0, 1, 0, 1, 0, 1, 0, 1, 1, 1],
                outputs: [0, 1, 0, 1, 0, 1, 0, 1],
              },
            ],
          },
        },
        {
          id: "build-4x4-ram",
          title: "Build Simple Memory (4×4 RAM)",
          description:
            "Create addressable memory with 4 locations of 4 bits each using registers and a decoder.",
          tools: ["SWITCH", "LED", "CLOCK", "4BIT-REGISTER", "2TO4-DECODER"],
          unlocks: "4X4-RAM",
          verification: {
            inputs: 8, // A1-A0, D3-D0, Write-Enable, Clock
            outputs: 4, // Q3-Q0
            truthTable: [
              // Write to address 0
              { inputs: [0, 0, 1, 1, 0, 1, 1, 1], outputs: [1, 1, 0, 1] },
              // Read from address 0
              { inputs: [0, 0, 0, 0, 0, 0, 0, 0], outputs: [1, 1, 0, 1] },
              // Write to address 1
              { inputs: [0, 1, 0, 1, 1, 0, 1, 1], outputs: [0, 1, 1, 0] },
            ],
          },
        },
      ],
    },
  ],
  theoryIndex: [
    {
      id: "digital-logic-fundamentals",
      title: "Digital Logic Fundamentals",
      description: "Core concepts of digital logic and Boolean algebra",
      content: "/theory/fundamentals.md",
    },
    {
      id: "circuit-design-principles",
      title: "Circuit Design Principles",
      description: "Best practices for designing digital circuits",
      content: "/theory/design-principles.md",
    },
    {
      id: "real-world-applications",
      title: "Real-World Applications",
      description: "How digital logic is used in modern technology",
      content: "/theory/applications.md",
    },
  ],
};

// Legacy curriculum array for backward compatibility
export const curriculum: Challenge[] = studyContent.modules.flatMap((module) =>
  module.challenges ? module.challenges.filter((c) => c !== undefined) : [],
);
