import { writable, get } from "svelte/store"; // Add 'get'
import { Simulation } from "./logic/Simulation";
import type { GateType, SavedChallengeState } from "./logic/types"; // Import GateType
import { curriculum, studyContent } from "./curriculum";
import { browser } from "$app/environment";

// --- New State ---
export const selectedGateIds = writable<string[]>([]);

const CLOCK_INTERVAL_MS = 1000; // 1 pulse per second

// This interval will run in the background and tell the simulation store
// to toggle any clock components that exist.
if (browser) {
  // Only run this logic in the browser
  setInterval(() => {
    // We use get() here because we don't want this interval to be a subscriber itself.
    // We just want to poke the simulation at a fixed rate.
    const sim = get(simulation);
    const clockGates = [...sim.gates.values()].filter(
      (g) => g.type === "CLOCK",
    );

    if (clockGates.length > 0) {
      // Tell the simulation store to toggle all clocks
      simulation.toggleClocks();
    }
  }, CLOCK_INTERVAL_MS);
}

function createSimulationStore() {
  const sim = new Simulation();
  const { subscribe, set, update } = writable(sim);

  return {
    subscribe,
    // Expose methods to modify the simulation, which will trigger updates
    addGate: (type: GateType, x: number, y: number) => {
      // Use GateType here
      update((s) => {
        const newGate = s.addGate(type, x, y);
        selectedGateIds.set([newGate.id]); // Automatically select the new gate
        return s;
      });
    },
    moveGate(gateId: string, dx: number, dy: number) {
      update(s => {
        const gate = s.gates.get(gateId);
        if (gate) {
          gate.x += dx;
          gate.y += dy;
        }
        return s;
      });
    },
    removeGate(gateId: string) {
      update((s) => {
        s.removeGate(gateId);
        return s;
      });
    },
    addWire: (fromPinId: string, toPinId: string) => {
      update((s) => {
        s.addWire(fromPinId, toPinId);
        return s;
      });
    },
    toggleSwitch: (gateId: string) => {
      update((s) => {
        s.toggleSwitch(gateId);
        return s;
      });
    },
    toggleClocks: () => {
      update((s) => {
        const clockGates = [...s.gates.values()].filter(
          (g) => g.type === "CLOCK",
        );
        for (const clock of clockGates) {
          s.toggleSwitch(clock.id); // Re-use the toggleSwitch logic!
        }
        return s;
      });
    },
    getPinCoords: (pinId: string) => {
      // Use get() from svelte/store to read the store value without subscribing
      return get(simulation).getPinCoords(pinId);
    },
    reset: () => {
      set(new Simulation());
      selectedGateIds.set([]);
    },
    saveState: () => {
      return get(simulation).saveState();
    },
    loadState: (state: SavedChallengeState) => {
      update((s) => {
        s.loadState(state);
        return s;
      });
    },
  };
}

// Navigation state for the learning hub
export type NavigationView =
  | "main-menu"
  | "module"
  | "challenge"
  | "theory"
  | "learning-item";

interface NavigationState {
  currentView: NavigationView;
  currentModuleId: string | null;
  currentChallengeId: string | null;
  currentTheoryId: string | null;
  currentLearningItemId: string | null;
}

function createNavigationStore() {
  const { subscribe, set } = writable<NavigationState>({
    currentView: "main-menu",
    currentModuleId: null,
    currentChallengeId: null,
    currentTheoryId: null,
    currentLearningItemId: null,
  });

  return {
    subscribe,
    goToMainMenu: () => {
      set({
        currentView: "main-menu",
        currentModuleId: null,
        currentChallengeId: null,
        currentTheoryId: null,
        currentLearningItemId: null,
      });
    },
    goToModule: (moduleId: string) => {
      set({
        currentView: "module",
        currentModuleId: moduleId,
        currentChallengeId: null,
        currentTheoryId: null,
        currentLearningItemId: null,
      });
    },
    goToChallenge: (moduleId: string, challengeId: string) => {
      set({
        currentView: "challenge",
        currentModuleId: moduleId,
        currentChallengeId: challengeId,
        currentTheoryId: null,
        currentLearningItemId: null,
      });
    },
    goToTheory: (theoryId: string, moduleId?: string) => {
      set({
        currentView: "theory",
        currentModuleId: moduleId || null,
        currentChallengeId: null,
        currentTheoryId: theoryId,
        currentLearningItemId: null,
      });
    },
    goToLearningItem: (moduleId: string, learningItemId: string) => {
      set({
        currentView: "learning-item",
        currentModuleId: moduleId,
        currentChallengeId: null,
        currentTheoryId: null,
        currentLearningItemId: learningItemId,
      });
    },
  };
}

// Progress persistence utilities
const PROGRESS_STORAGE_KEY = "bytecrafted-progress";
const CHALLENGE_STATE_STORAGE_KEY = "bytecrafted-challenge-state";

interface ProgressData {
  levelIndex: number;
  unlockedTools: string[];
  completedChallenges: string[];
  completedLearningItems: string[]; // New field for tracking completed learning items
}

function loadProgressFromStorage(): ProgressData | null {
  if (!browser) return null;

  try {
    const stored = localStorage.getItem(PROGRESS_STORAGE_KEY);
    if (stored) {
      return JSON.parse(stored);
    }
  } catch (error) {
    console.warn("Failed to load progress from localStorage:", error);
  }
  return null;
}

function saveProgressToStorage(progress: ProgressData): void {
  if (!browser) return;

  try {
    localStorage.setItem(PROGRESS_STORAGE_KEY, JSON.stringify(progress));
  } catch (error) {
    console.warn("Failed to save progress to localStorage:", error);
  }
}

function loadChallengeStateFromStorage(
  challengeId: string,
): SavedChallengeState | null {
  if (!browser) return null;

  try {
    const stored = localStorage.getItem(
      `${CHALLENGE_STATE_STORAGE_KEY}-${challengeId}`,
    );
    if (stored) {
      return JSON.parse(stored);
    }
  } catch (error) {
    console.warn("Failed to load challenge state from localStorage:", error);
  }
  return null;
}

function saveChallengeStateToStorage(
  challengeId: string,
  state: SavedChallengeState,
): void {
  if (!browser) return;

  try {
    localStorage.setItem(
      `${CHALLENGE_STATE_STORAGE_KEY}-${challengeId}`,
      JSON.stringify(state),
    );
  } catch (error) {
    console.warn("Failed to save challenge state to localStorage:", error);
  }
}

function clearChallengeStateFromStorage(challengeId: string): void {
    if (!browser) return;

    try {
        localStorage.removeItem(`${CHALLENGE_STATE_STORAGE_KEY}-${challengeId}`);
    } catch (error) {
        console.warn('Failed to remove challenge state from localStorage:', error);
    }
}

interface ChallengeStoreState {
  levelIndex: number;
  unlockedTools: Set<GateType>;
  completedChallenges: Set<string>;
  completedLearningItems: Set<string>;
}

function createChallengeStore() {
  // Load initial state from localStorage or use defaults
  const savedProgress = loadProgressFromStorage();
  const defaultTools: GateType[] = ["SWITCH", "NAND", "LED", "CLOCK"];
  const initialState: ChallengeStoreState = {
    levelIndex: savedProgress?.levelIndex ?? 0,
    unlockedTools: new Set<GateType>(
      (savedProgress?.unlockedTools as GateType[]) ?? defaultTools,
    ),
    completedChallenges: new Set<string>(
      savedProgress?.completedChallenges ?? [],
    ),
    completedLearningItems: new Set<string>(
      savedProgress?.completedLearningItems ?? [],
    ),
  };

  const { subscribe, set, update } = writable(initialState);

  // Helper function to save current state to localStorage
  function saveCurrentState(store: ChallengeStoreState) {
    const progressData: ProgressData = {
      levelIndex: store.levelIndex,
      unlockedTools: Array.from(store.unlockedTools),
      completedChallenges: Array.from(store.completedChallenges),
      completedLearningItems: Array.from(store.completedLearningItems),
    };
    saveProgressToStorage(progressData);
    return store;
  }

  return {
    subscribe,
    advanceToNextLevel: () => {
      update((store) => {
        const currentChallenge = curriculum[store.levelIndex];
        if (currentChallenge.unlocks) {
          store.unlockedTools.add(currentChallenge.unlocks);
        }

        // Move to next level if it exists
        if (store.levelIndex < curriculum.length - 1) {
          store.levelIndex++;
        }

        simulation.reset(); // Reset the workbench for the new level
        return saveCurrentState(store);
      });
    },
    completeChallenge: (challengeId: string) => {
      update((store) => {
        store.completedChallenges.add(challengeId);

        // Find the challenge in the new learning items structure and unlock its tools
        let challengeToUnlock = null;

        // Search through all modules and their learning items
        for (const module of studyContent.modules) {
          if (module.learningItems) {
            for (const item of module.learningItems) {
              if (
                item.type === "challenge" &&
                item.challenge?.id === challengeId
              ) {
                challengeToUnlock = item.challenge;
                break;
              }
            }
          }
          if (challengeToUnlock) break;
        }

        // If not found in learning items, fall back to legacy challenges array
        if (!challengeToUnlock) {
          challengeToUnlock = curriculum.find((c) => c.id === challengeId);
        }

        if (challengeToUnlock?.unlocks) {
          store.unlockedTools.add(challengeToUnlock.unlocks);
        }

        // Module unlocking removed - all modules are now accessible

        simulation.reset(); // Reset the workbench
        return saveCurrentState(store);
      });
    },
    setCurrentChallenge: (challengeId: string) => {
      const savedState = loadChallengeStateFromStorage(challengeId);
      if (savedState) {
        simulation.loadState(savedState);
      } else {
        simulation.reset(); // Reset the workbench for the new challenge
      }

      update((store) => {
        const challengeIndex = curriculum.findIndex(
          (c) => c.id === challengeId,
        );
        if (challengeIndex !== -1) {
          store.levelIndex = challengeIndex;
        }
        return store;
      });
    },
    resetProgress: () => {
      // Clear localStorage and reset to initial state
      if (browser) {
        localStorage.removeItem(PROGRESS_STORAGE_KEY);
      }

      const resetState: ChallengeStoreState = {
        levelIndex: 0,
        unlockedTools: new Set<GateType>(["SWITCH", "NAND", "LED", "CLOCK"]),
        completedChallenges: new Set<string>(),
        completedLearningItems: new Set<string>(),
      };

      set(resetState);
      simulation.reset(); // Reset the workbench
    },
    completeLearningItem: (learningItemId: string) => {
      update((store) => {
        store.completedLearningItems.add(learningItemId);
        return saveCurrentState(store);
      });
    },


    saveCurrentChallengeState: () => {
      const state = get(simulation).saveState();
      const navigation = get(navigationStore);
      let challengeId = navigation.currentChallengeId;

      if (!challengeId) {
        const learningItemId = navigation.currentLearningItemId;
        if (learningItemId) {
          const module = studyContent.modules.find(
            (m) => m.id === navigation.currentModuleId,
          );
          const learningItem = module?.learningItems?.find(
            (item) => item.id === learningItemId,
          );
          if (learningItem?.type === "challenge" && learningItem.challenge) {
            challengeId = learningItem.challenge.id;
          }
        }
      }

      if (challengeId) {
        saveChallengeStateToStorage(challengeId, state);
      }
    },
    clearCurrentChallengeState: () => {
        const navigation = get(navigationStore);
        let challengeId = navigation.currentChallengeId;

        if (!challengeId) {
            const learningItemId = navigation.currentLearningItemId;
            if (learningItemId) {
                const module = studyContent.modules.find(
                    (m) => m.id === navigation.currentModuleId,
                );
                const learningItem = module?.learningItems?.find(
                    (item) => item.id === learningItemId,
                );
                if (learningItem?.type === "challenge" && learningItem.challenge) {
                    challengeId = learningItem.challenge.id;
                }
            }
        }

        if (challengeId) {
            clearChallengeStateFromStorage(challengeId);
        }
    }
  };
}

export const simulation = createSimulationStore();
export const challengeStore = createChallengeStore();

// Theme Management System
export type Theme = 'light' | 'dark';

const THEME_STORAGE_KEY = 'bytecrafted-theme';

function loadThemeFromStorage(): Theme {
  if (!browser) return 'dark'; // Default to dark theme on server

  try {
    const stored = localStorage.getItem(THEME_STORAGE_KEY);
    if (stored === 'light' || stored === 'dark') {
      return stored;
    }
  } catch (error) {
    console.warn('Failed to load theme from localStorage:', error);
  }

  // Default to dark theme (current design)
  return 'dark';
}

function saveThemeToStorage(theme: Theme): void {
  if (!browser) return;

  try {
    localStorage.setItem(THEME_STORAGE_KEY, theme);
  } catch (error) {
    console.warn('Failed to save theme to localStorage:', error);
  }
}

function createThemeStore() {
  const initialTheme = loadThemeFromStorage();
  const { subscribe, set, update } = writable<Theme>(initialTheme);

  // Apply theme to document root on initialization
  if (browser) {
    document.documentElement.setAttribute('data-theme', initialTheme);
  }

  return {
    subscribe,
    setTheme: (theme: Theme) => {
      set(theme);
      saveThemeToStorage(theme);
      if (browser) {
        document.documentElement.setAttribute('data-theme', theme);
      }
    },
    toggleTheme: () => {
      update(currentTheme => {
        const newTheme = currentTheme === 'light' ? 'dark' : 'light';
        saveThemeToStorage(newTheme);
        if (browser) {
          document.documentElement.setAttribute('data-theme', newTheme);
        }
        return newTheme;
      });
    },
    getCurrentTheme: () => get({ subscribe })
  };
}

export const themeStore = createThemeStore();
export const navigationStore = createNavigationStore();
