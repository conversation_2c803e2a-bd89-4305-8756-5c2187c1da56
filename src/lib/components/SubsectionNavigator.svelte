<script lang="ts">
  import type { TheorySubsection } from '$lib/curriculum';

  export let subsections: TheorySubsection[];
  export let currentSubsectionIndex: number = 0;
  export let onSubsectionChange: (index: number) => void;

  $: canGoBack = currentSubsectionIndex > 0;
  $: canGoForward = currentSubsectionIndex < subsections.length - 1;
  $: currentSubsection = subsections[currentSubsectionIndex];
  $: progress = `${currentSubsectionIndex + 1} of ${subsections.length}`;

  function goToPrevious() {
    if (canGoBack) {
      const newIndex = currentSubsectionIndex - 1;
      onSubsectionChange(newIndex);
    }
  }

  function goToNext() {
    if (canGoForward) {
      const newIndex = currentSubsectionIndex + 1;
      onSubsectionChange(newIndex);
    }
  }






</script>

<nav class="subsection-navigator" aria-label="Subsection navigation">
  <button
    class="nav-button prev-button"
    class:disabled={!canGoBack}
    on:click={goToPrevious}
    disabled={!canGoBack}
    title="Previous section"
  >
    <span class="nav-icon">←</span>
    <span class="nav-text">Previous</span>
  </button>

  <div class="page-indicator">
    <span class="page-text">{progress}</span>
  </div>

  <button
    class="nav-button next-button"
    class:disabled={!canGoForward}
    on:click={goToNext}
    disabled={!canGoForward}
    title="Next section"
  >
    <span class="nav-text">Next</span>
    <span class="nav-icon">→</span>
  </button>
</nav>

<style>
  .subsection-navigator {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-3) 0;
    border-top: 1px solid var(--border-muted);
    margin-top: var(--spacing-6);
    gap: var(--spacing-4);
  }

  .page-indicator {
    flex: 1;
    text-align: center;
  }

  .page-text {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    font-weight: var(--font-weight-medium);
  }

  .nav-button {
    display: flex;
    align-items: center;
    gap: var(--spacing-1);
    padding: var(--spacing-2) var(--spacing-3);
    background: transparent;
    color: var(--text-secondary);
    border: none;
    border-radius: var(--radius-sm);
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
    cursor: pointer;
    transition: all var(--transition-fast);
    min-width: 80px;
  }

  .nav-button:hover:not(.disabled) {
    color: var(--color-accent);
    background-color: var(--bg-surface);
  }

  .nav-button.disabled {
    opacity: 0.4;
    cursor: not-allowed;
  }

  .nav-icon {
    font-size: var(--font-size-base);
  }

  .nav-text {
    font-size: var(--font-size-sm);
  }

  /* Responsive Design */
  @media (max-width: 768px) {
    .subsection-navigator {
      gap: var(--spacing-2);
    }

    .nav-button {
      min-width: 60px;
      padding: var(--spacing-1) var(--spacing-2);
      font-size: var(--font-size-xs);
    }

    .page-text {
      font-size: var(--font-size-xs);
    }
  }

  @media (max-width: 480px) {
    .nav-button {
      min-width: 50px;
    }

    .nav-text {
      display: none;
    }
  }
</style>
