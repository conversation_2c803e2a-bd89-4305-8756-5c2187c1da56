<script lang="ts">
	 
	import { marked } from 'marked';

	export let theoryPath: string;

	let markdownHtml = '<p>Loading theory content...</p>';

	// Load theory content from markdown file
	async function loadTheoryContent(path: string) {
		try {
			const response = await fetch(path);
			if (!response.ok) {
				throw new Error(`Could not fetch theory content: ${response.statusText}`);
			}
			const markdownText = await response.text();
			markdownHtml = await marked.parse(markdownText);
		} catch (error) {
			console.error(error);
			markdownHtml = '<p>Could not load the theory content. Please try again later.</p>';
		}
	}

	// Load content when theoryPath changes
	$: {
		if (theoryPath) {
			loadTheoryContent(theoryPath);
		}
	}
</script>

<div class="theory-content-wrapper">
	 {@html markdownHtml}
</div>

<style>
	.theory-content-wrapper {
		color: var(--text-primary);
		line-height: var(--line-height-relaxed);
	}

	.theory-content-wrapper :global(h1) {
		color: var(--color-accent);
		font-size: var(--font-size-2xl);
		margin: 2rem 0 1rem 0;
		border-bottom: 2px solid var(--border-default);
		padding-bottom: 0.5rem;
		font-weight: var(--font-weight-bold);
	}

	.theory-content-wrapper :global(h2) {
		color: var(--color-success);
		font-size: var(--font-size-xl);
		margin: 1.5rem 0 1rem 0;
		font-weight: var(--font-weight-semibold);
	}

	.theory-content-wrapper :global(h3) {
		color: var(--color-warning);
		font-size: var(--font-size-lg);
		margin: 1rem 0 0.5rem 0;
		font-weight: var(--font-weight-medium);
	}

	.theory-content-wrapper :global(h4) {
		color: var(--text-purple);
		font-size: var(--font-size-base);
		margin: 1rem 0 0.5rem 0;
		font-weight: var(--font-weight-medium);
	}

	.theory-content-wrapper :global(p) {
		margin: var(--spacing-4) 0;
		text-align: justify;
		color: var(--text-primary);
		line-height: var(--line-height-relaxed);
	}

	.theory-content-wrapper :global(ul),
	.theory-content-wrapper :global(ol) {
		margin: var(--spacing-4) 0;
		padding-left: var(--spacing-8);
		color: var(--text-primary);
	}

	.theory-content-wrapper :global(li) {
		margin: var(--spacing-2) 0;
	}

	.theory-content-wrapper :global(code) {
		background-color: var(--bg-surface);
		color: var(--color-error);
		padding: var(--spacing-1) var(--spacing-2);
		border-radius: var(--radius-sm);
		font-family: var(--font-family-mono);
		border: 1px solid var(--border-muted);
	}

	.theory-content-wrapper :global(pre) {
		background-color: var(--bg-primary);
		border: 1px solid var(--border-default);
		border-radius: var(--radius-lg);
		padding: var(--spacing-4);
		overflow-x: auto;
		margin: var(--spacing-4) 0;
		box-shadow: var(--shadow-sm);
	}

	.theory-content-wrapper :global(pre code) {
		background: none;
		padding: 0;
		color: var(--text-primary);
		border: none;
	}

	.theory-content-wrapper :global(blockquote) {
		border-left: 4px solid var(--color-accent);
		margin: var(--spacing-4) 0;
		padding: var(--spacing-4) var(--spacing-6);
		background-color: var(--bg-surface);
		border-radius: 0 var(--radius-lg) var(--radius-lg) 0;
		font-style: italic;
	}

	.theory-content-wrapper :global(table) {
		border-collapse: collapse;
		width: 100%;
		margin: var(--spacing-4) 0;
		background-color: var(--bg-secondary);
		border-radius: var(--radius-lg);
		overflow: hidden;
		border: 1px solid var(--border-default);
	}

	.theory-content-wrapper :global(th),
	.theory-content-wrapper :global(td) {
		border: 1px solid var(--border-default);
		padding: var(--spacing-3);
		text-align: left;
	}

	.theory-content-wrapper :global(th) {
		background-color: var(--bg-surface);
		color: var(--color-accent);
		font-weight: var(--font-weight-semibold);
	}

	.theory-content-wrapper :global(tr:nth-child(even)) {
		background-color: var(--bg-tertiary);
	}

	.theory-content-wrapper :global(a) {
		color: var(--color-accent);
		text-decoration: underline;
		transition: color var(--transition-fast);
	}

	.theory-content-wrapper :global(a:hover) {
		color: var(--color-success);
	}

	.theory-content-wrapper :global(strong) {
		color: var(--color-warning);
		font-weight: var(--font-weight-semibold);
	}

	.theory-content-wrapper :global(em) {
		color: var(--text-purple);
		font-style: italic;
	}

	.theory-content-wrapper :global(hr) {
		border: none;
		border-top: 2px solid var(--border-default);
		margin: var(--spacing-8) 0;
	}

	/* Special styling for theory-specific elements */
	.theory-content-wrapper :global(.highlight) {
		background-color: var(--color-warning);
		color: var(--text-inverse);
		padding: var(--spacing-1) var(--spacing-2);
		border-radius: var(--radius-sm);
		font-weight: var(--font-weight-semibold);
	}

	.theory-content-wrapper :global(.note) {
		background-color: var(--bg-surface);
		border-left: 4px solid var(--color-success);
		padding: var(--spacing-4);
		margin: var(--spacing-4) 0;
		border-radius: 0 var(--radius-lg) var(--radius-lg) 0;
	}

	.theory-content-wrapper :global(.warning) {
		background-color: var(--bg-surface);
		border-left: 4px solid var(--color-error);
		padding: var(--spacing-4);
		margin: var(--spacing-4) 0;
		border-radius: 0 var(--radius-lg) var(--radius-lg) 0;
	}
</style>
