<script lang="ts">
  import { onMount } from 'svelte';
  import { marked } from 'marked';
  import type { LearningItem } from '$lib/curriculum';
  import SubsectionNavigator from './SubsectionNavigator.svelte';
  import TheoryContent from './TheoryContent.svelte';

  export let learningItem: LearningItem;

  let currentSubsectionIndex = 0;
  let markdownHtml = '<p>Loading content...</p>';
  let isLoading = false;

  // Check if this learning item has subsections
  $: hasSubsections = learningItem.subsections && learningItem.subsections.length > 0;
  $: subsections = learningItem.subsections || [];
  $: currentSubsection = hasSubsections ? subsections[currentSubsectionIndex] : null;

  // Load content whenever the current subsection changes
  $: if (currentSubsection) {
    loadContentForCurrentState();
  }

  // Initialize on mount
  onMount(() => {
    currentSubsectionIndex = 0;
    if (hasSubsections) {
      loadContentForCurrentState();
    }
  });

  async function loadContentForCurrentState() {
    // Prevent multiple simultaneous loads
    if (isLoading) return;

    if (!hasSubsections || !currentSubsection) {
      markdownHtml = '<p>No content available.</p>';
      return;
    }

    isLoading = true;
    markdownHtml = '<p>Loading content...</p>';

    try {
      // Check if content is a path or inline markdown
      if (currentSubsection.content.startsWith('/')) {
        // It's a file path
        const response = await fetch(currentSubsection.content);
        if (!response.ok) {
          throw new Error(`Could not fetch content: ${response.statusText}`);
        }
        const markdownText = await response.text();
        markdownHtml = await marked.parse(markdownText);
      } else {
        // It's inline markdown content
        markdownHtml = await marked.parse(currentSubsection.content);
      }
    } catch (error) {
      console.error('Error loading subsection content:', error);
      markdownHtml = '<p>Could not load the content. Please try again later.</p>';
    } finally {
      isLoading = false;
    }
  }

  function handleSubsectionChange(newIndex: number) {
    currentSubsectionIndex = newIndex;
    // The reactive statement will handle loading the new content
  }
</script>

<div class="subsection-theory-content">
  {#if hasSubsections}
    <!-- Subsection-based content -->
    <div class="subsection-content">
      <div class="theory-content-wrapper">
        {@html markdownHtml}
      </div>
    </div>

    <SubsectionNavigator
      {subsections}
      {currentSubsectionIndex}
      onSubsectionChange={handleSubsectionChange}
    />
  {:else}
    <!-- Fallback to regular theory content -->
    {#if learningItem.content}
      <TheoryContent theoryPath={learningItem.content} />
    {:else}
      <div class="no-content">
        <h2>No Content Available</h2>
        <p>This learning item doesn't have any content configured.</p>
      </div>
    {/if}
  {/if}
</div>

<style>
  .subsection-theory-content {
    max-width: 1000px;
    margin: 0 auto;
    padding: var(--spacing-4);
  }

  .subsection-content {
    background-color: var(--bg-card);
    border: 1px solid var(--border-default);
    border-radius: var(--radius-lg);
    padding: var(--spacing-6);
    box-shadow: var(--shadow-sm);
    animation: fadeIn 0.3s ease-out;
  }

  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: translateY(10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  .theory-content-wrapper {
    color: var(--text-primary);
    line-height: var(--line-height-relaxed);
  }

  /* Enhanced markdown styling for subsections */
  .theory-content-wrapper :global(h1) {
    color: var(--color-accent);
    font-size: var(--font-size-2xl);
    font-weight: var(--font-weight-bold);
    margin: 0 0 var(--spacing-6) 0;
    border-bottom: 2px solid var(--border-default);
    padding-bottom: var(--spacing-3);
    line-height: var(--line-height-tight);
  }

  .theory-content-wrapper :global(h2) {
    color: var(--color-success);
    font-size: var(--font-size-xl);
    font-weight: var(--font-weight-semibold);
    margin: var(--spacing-8) 0 var(--spacing-4) 0;
    line-height: var(--line-height-snug);
  }

  .theory-content-wrapper :global(h3) {
    color: var(--color-warning);
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-semibold);
    margin: var(--spacing-6) 0 var(--spacing-3) 0;
    line-height: var(--line-height-snug);
  }

  .theory-content-wrapper :global(h4) {
    color: var(--text-purple);
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-medium);
    margin: var(--spacing-4) 0 var(--spacing-2) 0;
    line-height: var(--line-height-normal);
  }

  .theory-content-wrapper :global(p) {
    margin: var(--spacing-4) 0;
    text-align: justify;
    font-size: var(--font-size-sm);
    line-height: var(--line-height-relaxed);
  }

  .theory-content-wrapper :global(ul),
  .theory-content-wrapper :global(ol) {
    margin: var(--spacing-4) 0;
    padding-left: var(--spacing-8);
    line-height: var(--line-height-relaxed);
  }

  .theory-content-wrapper :global(li) {
    margin: var(--spacing-2) 0;
    font-size: var(--font-size-sm);
  }

  .theory-content-wrapper :global(code) {
    background-color: var(--bg-surface);
    color: var(--color-error);
    padding: var(--spacing-1) var(--spacing-2);
    border-radius: var(--radius-sm);
    font-family: var(--font-family-mono);
    font-size: 0.85em;
    border: 1px solid var(--border-muted);
  }

  .theory-content-wrapper :global(pre) {
    background-color: var(--bg-primary);
    border: 1px solid var(--border-default);
    border-radius: var(--radius-lg);
    padding: var(--spacing-4);
    overflow-x: auto;
    margin: var(--spacing-4) 0;
    box-shadow: var(--shadow-sm);
  }

  .theory-content-wrapper :global(pre code) {
    background: none;
    padding: 0;
    color: var(--text-primary);
    border: none;
  }

  .theory-content-wrapper :global(blockquote) {
    border-left: 4px solid var(--color-accent);
    margin: var(--spacing-4) 0;
    padding: var(--spacing-4) var(--spacing-6);
    background-color: var(--bg-surface);
    border-radius: 0 var(--radius-lg) var(--radius-lg) 0;
    font-style: italic;
  }

  .theory-content-wrapper :global(table) {
    border-collapse: collapse;
    width: 100%;
    margin: var(--spacing-4) 0;
    background-color: var(--bg-surface);
    border-radius: var(--radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow-sm);
  }

  .theory-content-wrapper :global(th),
  .theory-content-wrapper :global(td) {
    border: 1px solid var(--border-default);
    padding: var(--spacing-3);
    text-align: left;
    font-size: var(--font-size-sm);
  }

  .theory-content-wrapper :global(th) {
    background-color: var(--bg-elevated);
    color: var(--color-accent);
    font-weight: var(--font-weight-semibold);
  }

  .theory-content-wrapper :global(tr:nth-child(even)) {
    background-color: var(--bg-secondary);
  }

  .theory-content-wrapper :global(a) {
    color: var(--color-accent);
    text-decoration: underline;
    transition: color var(--transition-fast);
  }

  .theory-content-wrapper :global(a:hover) {
    color: var(--color-accent-hover);
  }

  .theory-content-wrapper :global(strong) {
    color: var(--color-warning);
    font-weight: var(--font-weight-semibold);
  }

  .theory-content-wrapper :global(em) {
    color: var(--text-purple);
    font-style: italic;
  }

  .theory-content-wrapper :global(hr) {
    border: none;
    border-top: 2px solid var(--border-default);
    margin: var(--spacing-8) 0;
  }

  /* Special content blocks */
  .theory-content-wrapper :global(.highlight) {
    background-color: var(--color-warning);
    color: var(--text-inverse);
    padding: var(--spacing-1) var(--spacing-2);
    border-radius: var(--radius-sm);
    font-weight: var(--font-weight-semibold);
  }

  .theory-content-wrapper :global(.note) {
    background-color: var(--bg-surface);
    border-left: 4px solid var(--color-success);
    padding: var(--spacing-4);
    margin: var(--spacing-4) 0;
    border-radius: 0 var(--radius-lg) var(--radius-lg) 0;
  }

  .theory-content-wrapper :global(.warning) {
    background-color: var(--bg-surface);
    border-left: 4px solid var(--color-error);
    padding: var(--spacing-4);
    margin: var(--spacing-4) 0;
    border-radius: 0 var(--radius-lg) var(--radius-lg) 0;
  }

  .no-content {
    text-align: center;
    padding: var(--spacing-12);
    color: var(--text-secondary);
  }

  .no-content h2 {
    color: var(--text-primary);
    margin-bottom: var(--spacing-4);
  }

  /* Responsive design */
  @media (max-width: 768px) {
    .subsection-theory-content {
      padding: var(--spacing-3);
    }

    .subsection-content {
      padding: var(--spacing-4);
    }

    .theory-content-wrapper :global(h1) {
      font-size: var(--font-size-xl);
    }

    .theory-content-wrapper :global(h2) {
      font-size: var(--font-size-lg);
    }
  }

  @media (max-width: 480px) {
    .subsection-theory-content {
      padding: var(--spacing-2);
    }

    .subsection-content {
      padding: var(--spacing-3);
    }
  }
</style>
