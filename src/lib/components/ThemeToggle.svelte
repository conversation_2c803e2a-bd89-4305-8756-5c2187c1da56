<script lang="ts">
  import { themeStore } from '$lib/store';
  import { onMount } from 'svelte';

  let mounted = false;

  onMount(() => {
    mounted = true;
  });

  function handleToggle() {
    themeStore.toggleTheme();
  }

  function handleKeydown(event: KeyboardEvent) {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      handleToggle();
    }
  }
</script>

{#if mounted}
  <button
    class="theme-toggle"
    on:click={handleToggle}
    on:keydown={handleKeydown}
    aria-label="Toggle between light and dark theme"
    title="Toggle theme"
  >
    <div class="toggle-track">
      <div class="toggle-thumb" class:dark={$themeStore === 'dark'}>
        <span class="toggle-icon">
          {#if $themeStore === 'dark'}
            🌙
          {:else}
            ☀️
          {/if}
        </span>
      </div>
    </div>
    <span class="toggle-label">
      {$themeStore === 'dark' ? 'Dark' : 'Light'}
    </span>
  </button>
{/if}

<style>
  .theme-toggle {
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
    background: transparent;
    border: 1px solid var(--border-default);
    border-radius: var(--radius-md);
    padding: var(--spacing-2) var(--spacing-3);
    cursor: pointer;
    transition: all var(--transition-fast);
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
    color: var(--text-primary);
    white-space: nowrap;
  }

  .theme-toggle:hover {
    background-color: var(--bg-surface);
    border-color: var(--color-accent);
    transform: translateY(-1px);
    box-shadow: var(--shadow-sm);
  }

  .theme-toggle:focus-visible {
    outline: 2px solid var(--color-accent);
    outline-offset: 2px;
  }

  .theme-toggle:active {
    transform: translateY(0);
    box-shadow: var(--shadow-xs);
  }

  .toggle-track {
    position: relative;
    width: 40px;
    height: 20px;
    background-color: var(--bg-tertiary);
    border: 1px solid var(--border-default);
    border-radius: 10px;
    transition: all var(--transition-fast);
  }

  .theme-toggle:hover .toggle-track {
    border-color: var(--color-accent);
  }

  .toggle-thumb {
    position: absolute;
    top: 1px;
    left: 1px;
    width: 16px;
    height: 16px;
    background-color: var(--bg-elevated);
    border: 1px solid var(--border-subtle);
    border-radius: 50%;
    transition: all var(--transition-fast);
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: var(--shadow-xs);
  }

  .toggle-thumb.dark {
    transform: translateX(20px);
    background-color: var(--color-accent);
    border-color: var(--color-accent);
  }

  .toggle-icon {
    font-size: 10px;
    line-height: 1;
    transition: all var(--transition-fast);
  }

  .toggle-label {
    font-size: var(--font-size-xs);
    color: var(--text-secondary);
    transition: color var(--transition-fast);
  }

  .theme-toggle:hover .toggle-label {
    color: var(--text-primary);
  }

  /* Responsive adjustments */
  @media (max-width: 768px) {
    .toggle-label {
      display: none;
    }
    
    .theme-toggle {
      padding: var(--spacing-2);
    }
  }
</style>
