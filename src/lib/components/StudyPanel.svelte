<script lang="ts">
	import { challengeStore, navigationStore, simulation } from '$lib/store';
	import { curriculum, studyContent, type Challenge } from '$lib/curriculum';
	import { get } from 'svelte/store';

    export let width: number = 400; // Default width for the panel

    // State for verification result
    let verificationResult: { success: boolean, message: string } | null = null;

    // Clear verification result when navigation changes
    $: if (currentView || currentChallengeId || currentModuleId || currentTheoryId) {
        verificationResult = null;
    }

    // Clear verification result when simulation changes (e.g., reset)
    $: if ($simulation) {
        verificationResult = null;
    }
    // --- Logic for Resizing ---
	const MIN_WIDTH = 300; // Minimum width in pixels
	const MAX_WIDTH = 800; // Maximum width

	let markdownHtml = '<p>Loading study guide...</p>';

	// Get current navigation state
	$: currentView = $navigationStore.currentView;
	$: currentModuleId = $navigationStore.currentModuleId;
	$: currentChallengeId = $navigationStore.currentChallengeId;
	$: currentTheoryId = $navigationStore.currentTheoryId;
	$: currentLearningItemId = $navigationStore.currentLearningItemId;
	$: currentModule = currentModuleId ? studyContent.modules.find(m => m.id === currentModuleId) : null;
	$: currentChallenge = currentChallengeId ? curriculum.find(c => c.id === currentChallengeId) : null;

	$: currentLearningItem = currentLearningItemId && currentModule ?
		currentModule.learningItems?.find(item => item.id === currentLearningItemId) : null;

    function handleMouseDown(event: MouseEvent) {
		event.preventDefault(); // Prevent text selection during drag

		function handleMouseMove(moveEvent: MouseEvent) {
			const newWidth = moveEvent.clientX;

			// Clamp the width between min and max values
			if (newWidth >= MIN_WIDTH && newWidth <= MAX_WIDTH) {
				width = newWidth;
			}
		}

		function handleMouseUp() {
			window.removeEventListener('mousemove', handleMouseMove);
			window.removeEventListener('mouseup', handleMouseUp);
		}

		window.addEventListener('mousemove', handleMouseMove);
		window.addEventListener('mouseup', handleMouseUp);
	}




	// Static content for StudyPanel - completely static within modules
	$: {
		if ((currentView === 'module' || currentView === 'learning-item' || currentView === 'challenge') && currentModule) {
			// Show only module title - content stays identical regardless of current view
			markdownHtml = `<h2>${currentModule.title}</h2>`;
		} else if (currentView === 'main-menu') {
			markdownHtml = '<h1>8-Bit Computer Learning Hub</h1><p>Select a module to begin building your computer!</p>';
		}
		// Content remains completely static when navigating within a module
	}

	// Navigation functions
	function goToModule(moduleId: string) {
		// Find the module and auto-navigate to first theory item
		const module = studyContent.modules.find(m => m.id === moduleId);
		if (module && module.learningItems && module.learningItems.length > 0) {
			// Find the first theory item
			const firstTheoryItem = module.learningItems.find(item => item.type === 'theory');
			if (firstTheoryItem) {
				// Auto-navigate to the first theory item instead of module view
				goToLearningItem(moduleId, firstTheoryItem.id);
				return;
			}
		}
		// Fallback to module view if no theory items found
		navigationStore.goToModule(moduleId);
	}

	function goToChallenge(moduleId: string, challengeId: string) {
		navigationStore.goToChallenge(moduleId, challengeId);
		challengeStore.setCurrentChallenge(challengeId);
	}

	function goToTheory(theoryId: string, moduleId?: string) {
		navigationStore.goToTheory(theoryId, moduleId);
	}

	function goToLearningItem(moduleId: string, learningItemId: string) {
		navigationStore.goToLearningItem(moduleId, learningItemId);

		// If it's a challenge type, also set the current challenge
		const module = studyContent.modules.find(m => m.id === moduleId);
		const learningItem = module?.learningItems?.find(item => item.id === learningItemId);
		if (learningItem?.type === 'challenge' && learningItem.challenge) {
			challengeStore.setCurrentChallenge(learningItem.challenge.id);
		}
	}

	function goBackToMainMenu() {
		navigationStore.goToMainMenu();
	}



	// Challenge verification function
	async function checkWork() {
		// Get the current challenge from either direct challenge view or learning item challenge
		let challengeToCheck = currentChallenge;

		// If we're in a learning item view with a challenge, use that challenge
		if (currentView === 'learning-item' && currentLearningItem && currentLearningItem.type === 'challenge' && currentLearningItem.challenge) {
			challengeToCheck = currentLearningItem.challenge;
		}

		if (!challengeToCheck) return;

		const simInstance = get(simulation);
		const result = await simInstance.checkSolution(challengeToCheck);

		verificationResult = result;
		if (result.success) {
			// Mark challenge as completed
			challengeStore.completeChallenge(challengeToCheck.id);

			// Also mark the corresponding learning item as completed if we're in a learning item view
			if (currentView === 'learning-item' && currentLearningItem && currentLearningItem.type === 'challenge') {
				challengeStore.completeLearningItem(currentLearningItem.id);
			}

			// Trigger success modal instead of direct navigation
			// The modal will handle navigation back to module view
			triggerSuccessModal(challengeToCheck);
		}
	}

	// Function to trigger success modal - this will be called from parent component
	function triggerSuccessModal(challenge: Challenge) {
		// Dispatch event to parent component to show modal
		const event = new CustomEvent('challengeCompleted', {
			detail: { challenge }
		});
		window.dispatchEvent(event);
	}
</script>

<aside class="study-panel" style="width: {width}px">

	<div class="content">
		{#if currentView === 'main-menu'}
			<!-- Main Menu View -->
			<div class="main-menu">
				<h1>8-Bit Computer Learning Hub</h1>
				<!-- <p>Build a complete 8-bit computer from scratch, starting with basic logic gates and progressing to a fully programmable system.</p> -->

				<!-- Learning Modules Section -->
				<div class="modules-section">
					<h2>🎯 Learning Modules</h2>
					<p>Each module contains comprehensive theory and hands-on challenges to build your 8-bit computer step by step.</p>
				</div>

				<div class="modules-grid">
					{#each studyContent.modules as module (module.id)}
						<div class="module-card"
							 on:click={() => goToModule(module.id)}
							 on:keydown={(e) => e.key === 'Enter' && goToModule(module.id)}
							 tabindex="0"
							 role="button">
							<h3>{module.title}</h3>
							<p>{module.description}</p>
						</div>
					{/each}
				</div>
			</div>
		{:else if (currentView === 'module' || currentView === 'learning-item' || currentView === 'challenge') && currentModule}
			<!-- Module/Learning Item/Challenge View - Always shows same content -->
			<div class="module-view">
				<div class="breadcrumb">
					<button class="breadcrumb-link" on:click={goBackToMainMenu}>← Learning Hub</button>
				</div>

				<h1>{currentModule.title}</h1>
				<div class="module-content">
					{@html markdownHtml}
				</div>

				<!-- Learning Items List -->
				<div class="learning-items">

					{#if currentModule.learningItems && currentModule.learningItems.length > 0}
						<div class="items-list">
							{#each currentModule.learningItems as item, index (item.id)}
								{@const isCompleted = item.type === 'challenge' ?
									(item.challenge && $challengeStore.completedChallenges.has(item.challenge.id)) :
									$challengeStore.completedLearningItems.has(item.id)}

								<div class="learning-item"
									 class:completed={isCompleted}
									 class:active={currentLearningItemId === item.id}
									 on:click={() => goToLearningItem(currentModule.id, item.id)}
									 on:keydown={(e) => e.key === 'Enter' && goToLearningItem(currentModule.id, item.id)}
									 tabindex="0"
									 role="button">
									<div class="item-number">{index + 1}</div>
									<div class="item-type-icon">
										{#if item.type === 'theory'}📖
										{:else if item.type === 'challenge'}🎯
										{:else if item.type === 'video'}🎥
										{:else}📄{/if}
									</div>
									<div class="item-title">{item.title}</div>
									<div class="item-status">
										{#if isCompleted}✅
										{:else}⭕{/if}
									</div>
								</div>
							{/each}
						</div>
					{:else}
						<!-- Fallback to legacy structure -->
						<div class="legacy-structure">
							{#if currentModule.theory}
								<div class="path-item theory-item">
									<div class="item-header">
										<div class="item-icon">📖</div>
										<div class="item-info">
											<h3>Theory: {currentModule.theory.title}</h3>
											<p>{currentModule.theory.description}</p>
										</div>
									</div>
									<button
										class="path-button theory-button"
										on:click={() => currentModule.theory && goToTheory(currentModule.theory.id, currentModule.id)}
									>
										Study Theory
									</button>
								</div>
							{/if}

							{#if currentModule.challenges}
								<div class="path-section">
									<h3>🎯 Practical Challenges</h3>
									<div class="challenges-list">
										{#each currentModule.challenges as challenge (challenge.id)}
											<div class="path-item challenge-item"
												 class:completed={$challengeStore.completedChallenges.has(challenge.id)}>
												<div class="item-header">
													<div class="challenge-status">
														{#if $challengeStore.completedChallenges.has(challenge.id)}
															✅
														{:else}
															⭕
														{/if}
													</div>
													<div class="item-info">
														<h4>{challenge.title}</h4>
														<p>{challenge.description}</p>
													</div>
												</div>
												<button
													class="path-button challenge-button"
													on:click={() => goToChallenge(currentModule.id, challenge.id)}
												>
													Start Challenge
												</button>
											</div>
										{/each}
									</div>
								</div>
							{/if}
						</div>
					{/if}
				</div>
			</div>
		{/if}
	</div>

	<div class="footer">
		{#if currentView === 'learning-item' && currentLearningItem}
			{#if currentLearningItem.type === 'theory'}
				<button
					class="next-btn theory-cta"
					on:click={() => {
						challengeStore.completeLearningItem(currentLearningItem.id);
					}}
				>
					Mark as Complete
				</button>
			{:else if currentLearningItem.type === 'challenge'}
				<button class="next-btn" on:click={checkWork}>Check My Work</button>
				{#if verificationResult && !verificationResult.success}
					<p class="error-msg">{verificationResult.message}</p>
				{/if}
			{/if}
		{:else if currentView === 'challenge'}
			<button class="next-btn" on:click={checkWork}>Check My Work</button>
			{#if verificationResult && !verificationResult.success}
				<p class="error-msg">{verificationResult.message}</p>
			{/if}
		{/if}
	</div>

	<div class="resizer" on:mousedown={handleMouseDown} role="separator" aria-label="Resize panel"></div>
</aside>

<style>
	.study-panel {
		position: fixed;
		top: 80px; /* Start below the header */
		left: 0;
		height: calc(100vh - 80px); /* Adjust height to account for header */
		background-color: var(--bg-secondary);
		border-right: 1px solid var(--border-default);
		z-index: var(--z-fixed);
		display: flex;
		flex-direction: column;
		transition: transform var(--transition-normal);
		transform: translateX(0);
		box-shadow: var(--shadow-lg);
	}

	.content {
		flex-grow: 1;
		padding: var(--spacing-6);
		overflow-y: auto;
        width: 100%;
        box-sizing: border-box;
        font-size: var(--font-size-sm);
        line-height: var(--line-height-relaxed);
	}

	.footer {
        flex-shrink: 0;
		padding: var(--spacing-4) var(--spacing-6);
		border-top: 1px solid var(--border-default);
		background-color: var(--bg-tertiary);
	}

	.next-btn {
		width: 100%;
		padding: var(--spacing-3) var(--spacing-4);
		font-size: var(--font-size-sm);
		font-weight: var(--font-weight-medium);
		background-color: var(--color-accent);
		color: var(--text-inverse);
		border: none;
		border-radius: var(--radius-md);
		cursor: pointer;
		margin-bottom: var(--spacing-3);
		transition: all var(--transition-fast);
		display: flex;
		align-items: center;
		justify-content: center;
		gap: var(--spacing-2);
	}

	.next-btn:hover {
		background-color: var(--color-accent-hover);
		transform: translateY(-1px);
		box-shadow: var(--shadow-md);
	}

	.theory-cta {
		background-color: var(--color-success);
		color: var(--text-inverse);
	}

	.theory-cta:hover {
		background-color: var(--color-success-hover);
	}

	.theory-cta:hover {
		background-color: var(--color-success-hover);
	}

	.error-msg {
		color: var(--color-error);
		background-color: rgba(var(--color-error-rgb), 0.1);
		padding: var(--spacing-3);
		border-radius: var(--radius-md);
		border: 1px solid var(--color-error-muted);
		margin: 0;
		font-size: var(--font-size-sm);
		line-height: var(--line-height-normal);
	}

	/* Enhanced styles for the rendered markdown */
	:global(.content h1) {
		color: var(--color-accent);
		border-bottom: 2px solid var(--border-default);
		padding-bottom: var(--spacing-4);
		font-size: var(--font-size-2xl);
		font-weight: var(--font-weight-bold);
		line-height: var(--line-height-tight);
		margin: var(--spacing-6) 0 var(--spacing-4) 0;
	}

	:global(.content h2) {
		color: var(--text-purple);
		font-size: var(--font-size-xl);
		font-weight: var(--font-weight-semibold);
		line-height: var(--line-height-snug);
		margin: var(--spacing-6) 0 var(--spacing-3) 0;
	}

	:global(.content h3) {
		color: var(--color-warning);
		font-size: var(--font-size-lg);
		font-weight: var(--font-weight-semibold);
		line-height: var(--line-height-snug);
		margin: var(--spacing-5) 0 var(--spacing-3) 0;
	}

	:global(.content h4) {
		color: var(--color-success);
		font-size: var(--font-size-base);
		font-weight: var(--font-weight-medium);
		line-height: var(--line-height-normal);
		margin: var(--spacing-4) 0 var(--spacing-2) 0;
	}

	:global(.content p) {
		color: var(--text-primary);
		line-height: var(--line-height-relaxed);
		margin: var(--spacing-3) 0;
		font-size: var(--font-size-sm);
	}

	:global(.content ul), :global(.content ol) {
		color: var(--text-primary);
		line-height: var(--line-height-relaxed);
		margin: var(--spacing-3) 0;
		padding-left: var(--spacing-6);
		font-size: var(--font-size-sm);
	}

	:global(.content li) {
		margin: var(--spacing-2) 0;
	}

	:global(.content code) {
		background-color: var(--bg-surface);
		color: var(--color-error);
		padding: var(--spacing-1) var(--spacing-2);
		border-radius: var(--radius-sm);
		font-family: var(--font-family-mono);
		font-size: 0.85em;
		font-weight: var(--font-weight-medium);
		border: 1px solid var(--border-muted);
	}

	:global(.content pre) {
		background-color: var(--bg-card);
		border: 1px solid var(--border-default);
		border-radius: var(--radius-lg);
		padding: var(--spacing-4);
		overflow-x: auto;
		margin: var(--spacing-4) 0;
		box-shadow: var(--shadow-sm);
	}

	:global(.content pre code) {
		background: none;
		padding: 0;
		color: var(--text-primary);
		border: none;
	}

	:global(.content strong) {
		color: var(--color-warning);
		font-weight: 600;
	}

	:global(.content em) {
		color: var(--text-purple);
		font-style: italic;
	}

	/* Enhanced table styling for truth tables */
	:global(.content table) {
		width: 100%;
		border-collapse: collapse;
		margin: 1.5rem 0;
		background-color: var(--bg-secondary);
		border-radius: 6px;
		overflow: hidden;
		border: 1px solid var(--border-default);
	}

	:global(.content th) {
		background-color: var(--bg-surface);
		color: var(--color-accent);
		font-weight: 600;
		padding: 0.75rem;
		text-align: center;
		border-bottom: 2px solid var(--border-default);
		font-size: 0.9rem;
	}

	:global(.content td) {
		padding: 0.75rem;
		text-align: center;
		border-bottom: 1px solid var(--border-default);
		color: var(--text-secondary);
		font-family: var(--font-family-mono);
		font-size: 0.9rem;
	}

	:global(.content tr:last-child td) {
		border-bottom: none;
	}

	:global(.content tr:nth-child(even)) {
		background-color: var(--bg-tertiary);
	}

	:global(.content tr:hover) {
		background-color: var(--bg-surface);
	}

	:global(.content blockquote) {
		border-left: 4px solid var(--color-accent);
		margin: 1rem 0;
		padding: 0.5rem 1rem;
		background-color: var(--bg-tertiary);
		border-radius: 0 6px 6px 0;
		color: var(--text-secondary);
	}

	:global(.content hr) {
		border: none;
		border-top: 2px solid var(--border-default);
		margin: 2rem 0;
	}

    .resizer {
		position: absolute;
		top: 0;
		right: 0;
		width: 5px;
		height: 100%;
		cursor: ew-resize; /* East-West resize cursor */
		background-color: transparent;
		transition: background-color 0.2s;
	}

	.resizer:hover {
		background-color: var(--color-accent);
	}

	/* Learning Hub Styles */
	.main-menu {
		padding: 1rem 0;
	}

	.modules-grid {
		display: grid;
		gap: 1rem;
		margin-top: 2rem;
	}

	.module-card {
		background-color: var(--bg-secondary);
		border: 1px solid var(--border-default);
		border-radius: 8px;
		padding: 1.5rem;
		cursor: pointer;
		transition: all 0.2s ease;
		position: relative;
	}

	.module-card:hover:not(.locked) {
		border-color: var(--color-accent);
		background-color: var(--bg-tertiary);
	}

	.module-card h3 {
		color: var(--color-accent);
		margin: 0 0 0.5rem 0;
		font-size: 1.1rem;
	}

	.module-card p {
		color: var(--text-secondary);
		margin: 0;
		font-size: 0.9rem;
		line-height: 1.4;
	}

	.breadcrumb {
		margin-bottom: 1rem;
		font-size: 0.9rem;
	}

	.breadcrumb-link {
		background: none;
		border: none;
		color: var(--color-accent);
		cursor: pointer;
		text-decoration: underline;
		padding: 0;
		font-size: inherit;
	}

	.breadcrumb-link:hover {
		color: var(--color-success);
	}

	.module-view {
		padding: 0;
	}

	.module-content {
		margin: 1rem 0;
	}

	/* Learning Items Styles */
	.learning-items {
		margin-top: 2rem;
	}

	.items-list {
		display: flex;
		flex-direction: column;
		gap: 0.75rem;
	}

	.learning-item {
		display: flex;
		align-items: center;
		gap: 0.75rem;
		background-color: var(--bg-secondary);
		border: 1px solid var(--border-default);
		border-radius: 6px;
		padding: 0.75rem;
		cursor: pointer;
		transition: all 0.2s ease;
	}

	.learning-item:hover {
		border-color: var(--color-accent);
		background-color: var(--bg-tertiary);
	}

	.learning-item.completed {
		border-color: var(--color-success);
		background-color: var(--bg-tertiary);
	}

	.learning-item.active {
		border-color: var(--color-accent);
		background-color: var(--bg-elevated);
		box-shadow: 0 0 0 1px var(--color-accent);
	}

	.learning-item.active.completed {
		border-color: var(--color-success);
		background-color: var(--bg-elevated);
		box-shadow: 0 0 0 1px var(--color-success);
	}

	.item-number {
		background-color: var(--color-accent);
		color: var(--text-inverse);
		width: 1.5rem;
		height: 1.5rem;
		border-radius: 50%;
		display: flex;
		align-items: center;
		justify-content: center;
		font-weight: bold;
		font-size: 0.8rem;
		flex-shrink: 0;
	}

	.learning-item.completed .item-number {
		background-color: var(--color-success);
	}

	.item-type-icon {
		font-size: 1rem;
		flex-shrink: 0;
	}

	.item-title {
		color: var(--color-accent);
		font-size: 0.9rem;
		font-weight: 500;
		flex-grow: 1;
		line-height: 1.2;
	}

	.item-status {
		font-size: 1rem;
		flex-shrink: 0;
	}

	/* Legacy structure styles (for backward compatibility) */
	.legacy-structure {
		display: flex;
		flex-direction: column;
		gap: 1.5rem;
	}



	.path-section {
		margin-top: 2rem;
	}

	.path-section h3 {
		color: var(--color-success);
		margin: 0 0 1rem 0;
		font-size: 1.1rem;
	}

	.path-item {
		background-color: var(--bg-secondary);
		border: 1px solid var(--border-default);
		border-radius: 8px;
		padding: 1.5rem;
		margin-bottom: 1rem;
		transition: all 0.2s ease;
	}

	.path-item:hover {
		border-color: var(--color-accent);
		background-color: var(--bg-tertiary);
	}

	.path-item.completed {
		border-color: var(--color-success);
		background-color: var(--bg-tertiary);
	}

	.item-header {
		display: flex;
		align-items: flex-start;
		gap: 1rem;
		margin-bottom: 1rem;
	}

	.item-icon {
		font-size: 1.5rem;
		flex-shrink: 0;
		margin-top: 0.2rem;
	}

	.challenge-status {
		font-size: 1.2rem;
		flex-shrink: 0;
		margin-top: 0.2rem;
	}

	.item-info h3 {
		color: var(--text-purple);
		margin: 0 0 0.5rem 0;
		font-size: 1.1rem;
	}

	.item-info h4 {
		color: var(--color-accent);
		margin: 0 0 0.5rem 0;
		font-size: 1rem;
	}

	.item-info p {
		color: var(--text-secondary);
		margin: 0;
		font-size: 0.9rem;
		line-height: 1.4;
	}

	.path-button {
		border: none;
		padding: 0.75rem 1.5rem;
		border-radius: 6px;
		font-size: 0.95rem;
		font-weight: bold;
		cursor: pointer;
		transition: all 0.2s ease;
		width: 100%;
	}

	.theory-button {
		background-color: var(--text-purple);
		color: var(--text-inverse);
	}

	.theory-button:hover {
		background-color: var(--text-purple);
		opacity: 0.9;
		transform: translateY(-1px);
		box-shadow: var(--shadow-md);
	}

	.challenge-button {
		background-color: var(--color-accent);
		color: var(--text-inverse);
	}

	.challenge-button:hover {
		background-color: var(--color-accent-hover);
		transform: translateY(-1px);
		box-shadow: var(--shadow-md);
	}

	.challenges-list {
		display: flex;
		flex-direction: column;
		gap: 0.75rem;
	}

	/* Modules Section */
	.modules-section h2 {
		color: var(--text-purple);
		margin: 0 0 0.5rem 0;
		font-size: 1.3rem;
	}

	.modules-section p {
		color: var(--text-secondary);
		margin: 0 0 1rem 0;
		font-size: 0.9rem;
	}

	.modules-section {
		margin-bottom: 1rem;
	}


</style>