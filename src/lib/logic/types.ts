export type PinType = "input" | "output";
export type GateType =
  | "NAND"
  | "SWITCH"
  | "LED"
  | "AND"
  | "XOR"
  | "CLOCK"
  | "D-LATCH"
  | "REGISTER"
  | "ALU"
  | "RAM"
  | "OR"
  | "NOT"
  | "D-FLIP-FLOP"
  | "4BIT-REGISTER"
  | "8BIT-REGISTER"
  | "2TO4-DECODER"
  | "4X4-RAM"
  | "FULL-ADDER"
  | "2BIT-ADDER"
  | "SR-LATCH";
export type PinValue = 0 | 1;

export interface Pin {
  id: string;
  gateId: string;
  type: PinType;
  value: PinValue;
}

export interface Gate {
  id: string;
  type: GateType;
  x: number;
  y: number;
  inputs: Pin[];
  outputs: Pin[];
}

export interface Wire {
  id: string;
  fromPinId: string;
  toPinId: string;
}

export interface SavedGateState {
  id: string;
  type: GateType;
  x: number;
  y: number;
}

export interface SavedWireState {
  fromGateId: string;
  fromPinIndex: number;
  toGateId: string;
  toPinIndex: number;
}

export interface SavedChallengeState {
  gates: SavedGateState[];
  wires: SavedWireState[];
}
