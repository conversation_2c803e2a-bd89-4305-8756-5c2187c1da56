import { v4 as uuid } from "uuid";
import type {
  <PERSON>,
  GateType,
  Pin,
  PinValue,
  Wire,
  SavedChallengeState,
  SavedGateState,
  SavedWireState,
} from "./types";
import type { Challenge } from "../curriculum";

export class Simulation {
  gates = new Map<string, Gate>();
  wires = new Map<string, Wire>();
  pins = new Map<string, Pin>();

  private updateQueue = new Set<string>();
  ramState = new Map<string, Uint8Array>(); // Maps RAM component ID to its memory
  flipFlopState = new Map<string, { lastClock: PinValue; qValue: PinValue }>(); // Maps D-FLIP-FLOP ID to its state

  // --- Public API to modify the circuit ---

  addGate(type: GateType, x: number, y: number): Gate {
    const newGate: Gate = {
      id: uuid(),
      type,
      x,
      y,
      inputs: [],
      outputs: [],
    };

    if (type === "NAND" || type === "AND" || type === "XOR" || type === "OR") {
      // Updated condition
      newGate.inputs.push(this.createPin(newGate.id, "input"));
      newGate.inputs.push(this.createPin(newGate.id, "input"));
      newGate.outputs.push(this.createPin(newGate.id, "output"));
    } else if (type === "NOT") {
      newGate.inputs.push(this.createPin(newGate.id, "input"));
      newGate.outputs.push(this.createPin(newGate.id, "output"));
    } else if (type === "SWITCH" || type === "CLOCK") {
      newGate.outputs.push(this.createPin(newGate.id, "output"));
    } else if (type === "LED") {
      newGate.inputs.push(this.createPin(newGate.id, "input"));
    } else if (type === "D-LATCH") {
      newGate.inputs.push(this.createPin(newGate.id, "input")); // Input D (Data)
      newGate.inputs.push(this.createPin(newGate.id, "input")); // Input E (Enable/Clock)
      newGate.outputs.push(this.createPin(newGate.id, "output")); // Output Q
    } else if (type === "D-FLIP-FLOP") {
      newGate.inputs.push(this.createPin(newGate.id, "input")); // Input D (Data)
      newGate.inputs.push(this.createPin(newGate.id, "input")); // Input Clock
      newGate.outputs.push(this.createPin(newGate.id, "output")); // Output Q
      newGate.outputs.push(this.createPin(newGate.id, "output")); // Output Q̄ (not Q)

      // Initialize flip-flop state
      this.flipFlopState.set(newGate.id, { lastClock: 0, qValue: 0 });
    } else if (type === "REGISTER") {
      // 8 data inputs, 1 Load Enable input
      for (let i = 0; i < 8; i++)
        newGate.inputs.push(this.createPin(newGate.id, "input")); // Data In [0-7]
      newGate.inputs.push(this.createPin(newGate.id, "input")); // Load Pin [8]

      // 8 data outputs
      for (let i = 0; i < 8; i++)
        newGate.outputs.push(this.createPin(newGate.id, "output")); // Data Out [0-7]
    } else if (type === "ALU") {
      // Input A (8 pins), Input B (8 pins), Opcode (2 pins)
      for (let i = 0; i < 8; i++)
        newGate.inputs.push(this.createPin(newGate.id, "input")); // A[0-7]
      for (let i = 0; i < 8; i++)
        newGate.inputs.push(this.createPin(newGate.id, "input")); // B[8-15]
      newGate.inputs.push(this.createPin(newGate.id, "input")); // Opcode 0 [16]
      newGate.inputs.push(this.createPin(newGate.id, "input")); // Opcode 1 [17]

      // Result (8 pins)
      for (let i = 0; i < 8; i++)
        newGate.outputs.push(this.createPin(newGate.id, "output")); // Out [0-7]
    } else if (type === "RAM") {
      // Initialize the memory for this RAM component
      this.ramState.set(newGate.id, new Uint8Array(256));

      // 8 Address inputs, 8 Data inputs, 1 Write Enable pin
      for (let i = 0; i < 8; i++)
        newGate.inputs.push(this.createPin(newGate.id, "input")); // Address [0-7]
      for (let i = 0; i < 8; i++)
        newGate.inputs.push(this.createPin(newGate.id, "input")); // Data In [8-15]
      newGate.inputs.push(this.createPin(newGate.id, "input")); // Write Enable [16]

      // 8 Data outputs
      for (let i = 0; i < 8; i++)
        newGate.outputs.push(this.createPin(newGate.id, "output")); // Data Out [0-7]
    } else if (type === "4BIT-REGISTER") {
      // 4 data inputs, 1 Clock, 1 Load Enable input
      for (let i = 0; i < 4; i++)
        newGate.inputs.push(this.createPin(newGate.id, "input")); // Data In [0-3]
      newGate.inputs.push(this.createPin(newGate.id, "input")); // Clock Pin [4]
      newGate.inputs.push(this.createPin(newGate.id, "input")); // Load Pin [5]

      // 4 data outputs
      for (let i = 0; i < 4; i++)
        newGate.outputs.push(this.createPin(newGate.id, "output")); // Data Out [0-3]
    } else if (type === "8BIT-REGISTER") {
      // 8 data inputs, 1 Clock, 1 Load Enable input
      for (let i = 0; i < 8; i++)
        newGate.inputs.push(this.createPin(newGate.id, "input")); // Data In [0-7]
      newGate.inputs.push(this.createPin(newGate.id, "input")); // Clock Pin [8]
      newGate.inputs.push(this.createPin(newGate.id, "input")); // Load Pin [9]

      // 8 data outputs
      for (let i = 0; i < 8; i++)
        newGate.outputs.push(this.createPin(newGate.id, "output")); // Data Out [0-7]
    } else if (type === "2TO4-DECODER") {
      // 2 address inputs, 1 enable input
      newGate.inputs.push(this.createPin(newGate.id, "input")); // A0 [0]
      newGate.inputs.push(this.createPin(newGate.id, "input")); // A1 [1]
      newGate.inputs.push(this.createPin(newGate.id, "input")); // Enable [2]

      // 4 outputs
      for (let i = 0; i < 4; i++)
        newGate.outputs.push(this.createPin(newGate.id, "output")); // Y[0-3]
    } else if (type === "4X4-RAM") {
      // 2 address inputs, 4 data inputs, 1 Write Enable, 1 Clock
      newGate.inputs.push(this.createPin(newGate.id, "input")); // A0 [0]
      newGate.inputs.push(this.createPin(newGate.id, "input")); // A1 [1]
      for (let i = 0; i < 4; i++)
        newGate.inputs.push(this.createPin(newGate.id, "input")); // Data In [2-5]
      newGate.inputs.push(this.createPin(newGate.id, "input")); // Write Enable [6]
      newGate.inputs.push(this.createPin(newGate.id, "input")); // Clock [7]

      // 4 data outputs
      for (let i = 0; i < 4; i++)
        newGate.outputs.push(this.createPin(newGate.id, "output")); // Data Out [0-3]

      // Initialize memory for this 4x4 RAM (4 locations × 4 bits = 16 bits total)
      this.ramState.set(newGate.id, new Uint8Array(4)); // 4 locations, each storing 4-bit values
    } else if (type === "FULL-ADDER") {
      // 3 inputs: A, B, Carry-in
      newGate.inputs.push(this.createPin(newGate.id, "input")); // A [0]
      newGate.inputs.push(this.createPin(newGate.id, "input")); // B [1]
      newGate.inputs.push(this.createPin(newGate.id, "input")); // Carry-in [2]

      // 2 outputs: Sum, Carry-out
      newGate.outputs.push(this.createPin(newGate.id, "output")); // Sum [0]
      newGate.outputs.push(this.createPin(newGate.id, "output")); // Carry-out [1]
    } else if (type === "2BIT-ADDER") {
      // 5 inputs: A1, A0, B1, B0, Carry-in
      newGate.inputs.push(this.createPin(newGate.id, "input")); // A1 [0]
      newGate.inputs.push(this.createPin(newGate.id, "input")); // A0 [1]
      newGate.inputs.push(this.createPin(newGate.id, "input")); // B1 [2]
      newGate.inputs.push(this.createPin(newGate.id, "input")); // B0 [3]
      newGate.inputs.push(this.createPin(newGate.id, "input")); // Carry-in [4]

      // 3 outputs: Sum1, Sum0, Carry-out
      newGate.outputs.push(this.createPin(newGate.id, "output")); // Sum1 [0]
      newGate.outputs.push(this.createPin(newGate.id, "output")); // Sum0 [1]
      newGate.outputs.push(this.createPin(newGate.id, "output")); // Carry-out [2]
    } else if (type === "SR-LATCH") {
      // 2 inputs: S (Set), R (Reset)
      newGate.inputs.push(this.createPin(newGate.id, "input")); // S [0]
      newGate.inputs.push(this.createPin(newGate.id, "input")); // R [1]

      // 2 outputs: Q, Q̄
      newGate.outputs.push(this.createPin(newGate.id, "output")); // Q [0]
      newGate.outputs.push(this.createPin(newGate.id, "output")); // Q̄ [1]

      // Initialize SR-latch state (start in reset state)
      this.flipFlopState.set(newGate.id, { lastClock: 0, qValue: 0 });
    }

    this.gates.set(newGate.id, newGate);
    this.evaluateGate(newGate.id);
    return newGate;
  }

  removeGate(gateId: string) {
    const gate = this.gates.get(gateId);
    if (!gate) return;

    const pinsToRemove = [...gate.inputs, ...gate.outputs];
    const wiresToRemove = new Set<string>();

    // Find all wires connected to this gate's pins
    for (const pin of pinsToRemove) {
      this.pins.delete(pin.id);
      for (const wire of this.wires.values()) {
        if (wire.fromPinId === pin.id || wire.toPinId === pin.id) {
          wiresToRemove.add(wire.id);
        }
      }
    }

    // Remove the wires
    wiresToRemove.forEach((wireId) => this.wires.delete(wireId));

    // Remove the gate
    this.gates.delete(gateId);

    // This is a big change, we should probably re-evaluate everything
    // For simplicity in the MVP, we won't, but a full sim would need it.
  }

  addWire(fromPinId: string, toPinId: string): Wire | null {
    const fromPin = this.pins.get(fromPinId);
    const toPin = this.pins.get(toPinId);

    // Basic validation
    if (
      !fromPin ||
      !toPin ||
      fromPin.type === "input" ||
      toPin.type === "output"
    ) {
      console.error("Invalid wire connection");
      return null;
    }

    const newWire: Wire = { id: uuid(), fromPinId, toPinId };
    this.wires.set(newWire.id, newWire);

    // Propagate the initial value
    this.propagateValue(fromPinId);
    return newWire;
  }

  toggleSwitch(gateId: string) {
    const gate = this.gates.get(gateId);
    // Allow toggling for both SWITCH and CLOCK types
    if (!gate || (gate.type !== "SWITCH" && gate.type !== "CLOCK")) return;

    const outputPin = gate.outputs[0];
    outputPin.value = outputPin.value === 0 ? 1 : 0;
    this.pins.set(outputPin.id, outputPin);

    this.propagateValue(outputPin.id);
  }
  // --- Private Simulation Logic ---

  private pinsToNumber(pins: Pin[]): number {
    return pins.reduce((acc, pin, i) => acc + (pin.value << i), 0);
  }

  // --- Helper function to set output pins from a number ---
  private numberToPins(num: number, pins: Pin[]): void {
    pins.forEach((pin, i) => {
      pin.value = (num >> i) & 1 ? 1 : 0;
    });
  }

  private createPin(gateId: string, type: "input" | "output"): Pin {
    const pin: Pin = { id: uuid(), gateId, type, value: 0 };
    this.pins.set(pin.id, pin);
    return pin;
  }

  private propagateValue(startPinId: string) {
    const startPin = this.pins.get(startPinId)!;

    // Find all wires connected to this output pin
    for (const wire of this.wires.values()) {
      if (wire.fromPinId === startPinId) {
        const destPin = this.pins.get(wire.toPinId)!;

        // If the value changed, update the destination and queue the gate for evaluation
        if (destPin.value !== startPin.value) {
          destPin.value = startPin.value;
          this.pins.set(destPin.id, destPin);
          this.updateQueue.add(destPin.gateId);
        }
      }
    }
    this.runUpdateQueue();
  }

  private runUpdateQueue() {
    while (this.updateQueue.size > 0) {
      const gateId = this.updateQueue.values().next().value;
      if (gateId) {
        this.updateQueue.delete(gateId);
        this.evaluateGate(gateId);
      }
    }
  }

  private evaluateGate(gateId: string) {
    const gate = this.gates.get(gateId);
    if (!gate) return;

    // Use a switch statement for clarity
    switch (gate.type) {
      case "NAND": {
        const in1 = gate.inputs[0].value;
        const in2 = gate.inputs[1].value;
        const out = gate.outputs[0];
        const newValue: PinValue = in1 === 1 && in2 === 1 ? 0 : 1;

        if (out.value !== newValue) {
          out.value = newValue;
          this.pins.set(out.id, out);
          this.propagateValue(out.id);
        }
        break;
      }

      // --- NEW LOGIC ---
      case "AND": {
        const in1 = gate.inputs[0].value;
        const in2 = gate.inputs[1].value;
        const out = gate.outputs[0];
        const newValue: PinValue = in1 === 1 && in2 === 1 ? 1 : 0;

        if (out.value !== newValue) {
          out.value = newValue;
          this.pins.set(out.id, out);
          this.propagateValue(out.id);
        }
        break;
      }

      case "XOR": {
        const in1 = gate.inputs[0].value;
        const in2 = gate.inputs[1].value;
        const out = gate.outputs[0];
        const newValue: PinValue = in1 !== in2 ? 1 : 0;

        if (out.value !== newValue) {
          out.value = newValue;
          this.pins.set(out.id, out);
          this.propagateValue(out.id);
        }
        break;
      }

      case "OR": {
        const in1 = gate.inputs[0].value;
        const in2 = gate.inputs[1].value;
        const out = gate.outputs[0];
        const newValue: PinValue = in1 === 1 || in2 === 1 ? 1 : 0;

        if (out.value !== newValue) {
          out.value = newValue;
          this.pins.set(out.id, out);
          this.propagateValue(out.id);
        }
        break;
      }

      case "NOT": {
        const in1 = gate.inputs[0].value;
        const out = gate.outputs[0];
        const newValue: PinValue = in1 === 1 ? 0 : 1;

        if (out.value !== newValue) {
          out.value = newValue;
          this.pins.set(out.id, out);
          this.propagateValue(out.id);
        }
        break;
      }
      case "D-LATCH": {
        const d_in = gate.inputs[0].value; // Data input
        const e_in = gate.inputs[1].value; // Enable (Clock) input
        const q_out = gate.outputs[0];

        // The core logic of the D Latch:
        // If Enable (clock) is high, the output follows the Data input.
        // If Enable is low, the output holds its PREVIOUS value.
        if (e_in === 1) {
          // If Clock is HIGH (transparent mode)
          if (q_out.value !== d_in) {
            q_out.value = d_in;
            this.pins.set(q_out.id, q_out);
            this.propagateValue(q_out.id);
          }
        }
        // If e_in is 0, we do nothing, thus "latching" or "remembering" the last value.
        break;
      }

      case "D-FLIP-FLOP": {
        const d_in = gate.inputs[0].value; // Data input
        const clock_in = gate.inputs[1].value; // Clock input
        const q_out = gate.outputs[0]; // Q output
        const qbar_out = gate.outputs[1]; // Q̄ output

        // Initialize state if not exists
        if (!this.flipFlopState.has(gate.id)) {
          this.flipFlopState.set(gate.id, { lastClock: 0, qValue: 0 });
        }

        const state = this.flipFlopState.get(gate.id)!;

        // Edge-triggered: only update on rising edge (0 -> 1)
        if (state.lastClock === 0 && clock_in === 1) {
          // Rising edge detected, update the stored value
          state.qValue = d_in;
          this.flipFlopState.set(gate.id, state);

          // Update outputs
          if (q_out.value !== state.qValue) {
            q_out.value = state.qValue;
            this.pins.set(q_out.id, q_out);
            this.propagateValue(q_out.id);
          }

          const qbar_value: PinValue = state.qValue === 1 ? 0 : 1;
          if (qbar_out.value !== qbar_value) {
            qbar_out.value = qbar_value;
            this.pins.set(qbar_out.id, qbar_out);
            this.propagateValue(qbar_out.id);
          }
        }

        // Update last clock state
        state.lastClock = clock_in;
        this.flipFlopState.set(gate.id, state);
        break;
      }
      case "REGISTER": {
        const load_pin = gate.inputs[8].value;

        // If the Load pin is high, read the 8 data inputs and update the 8 outputs.
        if (load_pin === 1) {
          let hasChanged = false;
          for (let i = 0; i < 8; i++) {
            const data_in = gate.inputs[i].value;
            const data_out = gate.outputs[i];
            if (data_out.value !== data_in) {
              data_out.value = data_in;
              this.pins.set(data_out.id, data_out);
              hasChanged = true;
            }
          }
          // If any output changed, propagate the signal from all output pins
          if (hasChanged) {
            for (let i = 0; i < 8; i++) {
              this.propagateValue(gate.outputs[i].id);
            }
          }
        }
        // If Load is low, do nothing. The outputs hold their values.
        break;
      }

      case "4BIT-REGISTER": {
        const clock_pin = gate.inputs[4].value;
        const load_pin = gate.inputs[5].value;

        // Initialize state if not exists
        if (!this.flipFlopState.has(gate.id)) {
          this.flipFlopState.set(gate.id, { lastClock: 0, qValue: 0 });
        }

        const state = this.flipFlopState.get(gate.id)!;

        // Edge-triggered on rising clock edge AND load is high
        if (state.lastClock === 0 && clock_pin === 1 && load_pin === 1) {
          let hasChanged = false;
          for (let i = 0; i < 4; i++) {
            const data_in = gate.inputs[i].value;
            const data_out = gate.outputs[i];
            if (data_out.value !== data_in) {
              data_out.value = data_in;
              this.pins.set(data_out.id, data_out);
              hasChanged = true;
            }
          }
          // Propagate changes
          if (hasChanged) {
            for (let i = 0; i < 4; i++) {
              this.propagateValue(gate.outputs[i].id);
            }
          }
        }

        // Update last clock state
        state.lastClock = clock_pin;
        this.flipFlopState.set(gate.id, state);
        break;
      }

      case "8BIT-REGISTER": {
        const clock_pin = gate.inputs[8].value;
        const load_pin = gate.inputs[9].value;

        // Initialize state if not exists
        if (!this.flipFlopState.has(gate.id)) {
          this.flipFlopState.set(gate.id, { lastClock: 0, qValue: 0 });
        }

        const state = this.flipFlopState.get(gate.id)!;

        // Edge-triggered on rising clock edge AND load is high
        if (state.lastClock === 0 && clock_pin === 1 && load_pin === 1) {
          let hasChanged = false;
          for (let i = 0; i < 8; i++) {
            const data_in = gate.inputs[i].value;
            const data_out = gate.outputs[i];
            if (data_out.value !== data_in) {
              data_out.value = data_in;
              this.pins.set(data_out.id, data_out);
              hasChanged = true;
            }
          }
          // Propagate changes
          if (hasChanged) {
            for (let i = 0; i < 8; i++) {
              this.propagateValue(gate.outputs[i].id);
            }
          }
        }

        // Update last clock state
        state.lastClock = clock_pin;
        this.flipFlopState.set(gate.id, state);
        break;
      }
      case "ALU": {
        const inputA_pins = gate.inputs.slice(0, 8);
        const inputB_pins = gate.inputs.slice(8, 16);
        const opcode_pins = gate.inputs.slice(16, 18);
        const output_pins = gate.outputs.slice(0, 8);

        const numA = this.pinsToNumber(inputA_pins);
        const numB = this.pinsToNumber(inputB_pins);
        const opcode = this.pinsToNumber(opcode_pins);

        let result = 0;

        // Opcode 00: ADD, 01: SUB, 10: AND, 11: OR
        switch (opcode) {
          case 0b00: // ADD
            result = (numA + numB) & 0xff; // & 0xFF keeps it 8-bit
            break;
          case 0b01: // SUB
            result = (numA - numB) & 0xff;
            break;
          case 0b10: // AND
            result = numA & numB;
            break;
          case 0b11: // OR
            result = numA | numB;
            break;
        }

        const oldResult = this.pinsToNumber(output_pins);
        if (result !== oldResult) {
          this.numberToPins(result, output_pins);
          output_pins.forEach((p) => this.pins.set(p.id, p));
          output_pins.forEach((p) => this.propagateValue(p.id));
        }
        break;
      }
      case "RAM": {
        const memory = this.ramState.get(gate.id);
        if (!memory) break;

        const address_pins = gate.inputs.slice(0, 8);
        const data_in_pins = gate.inputs.slice(8, 16);
        const write_enable_pin = gate.inputs[16];
        const data_out_pins = gate.outputs.slice(0, 8);

        const address = this.pinsToNumber(address_pins);

        // --- Write Operation (Sequential Part) ---
        // This happens only when Write Enable is high.
        if (write_enable_pin.value === 1) {
          const data_in = this.pinsToNumber(data_in_pins);
          // Only write if the data is different to prevent unnecessary updates
          if (memory[address] !== data_in) {
            memory[address] = data_in;
          }
        }

        // --- Read Operation (Combinational Part) ---
        // The RAM is always outputting the data at the selected address.
        const data_out = memory[address];
        const old_data_out = this.pinsToNumber(data_out_pins);
        if (data_out !== old_data_out) {
          this.numberToPins(data_out, data_out_pins);
          data_out_pins.forEach((p) => this.pins.set(p.id, p));
          data_out_pins.forEach((p) => this.propagateValue(p.id));
        }
        break;
      }

      case "2TO4-DECODER": {
        const a0 = gate.inputs[0].value;
        const a1 = gate.inputs[1].value;
        const enable = gate.inputs[2].value;

        // Calculate address from inputs
        const address = a0 + (a1 << 1);

        // Update outputs
        for (let i = 0; i < 4; i++) {
          const output = gate.outputs[i];
          const newValue: PinValue = enable === 1 && i === address ? 1 : 0;

          if (output.value !== newValue) {
            output.value = newValue;
            this.pins.set(output.id, output);
            this.propagateValue(output.id);
          }
        }
        break;
      }

      case "4X4-RAM": {
        const memory = this.ramState.get(gate.id);
        if (!memory) break;

        const a0 = gate.inputs[0].value;
        const a1 = gate.inputs[1].value;
        const data_in_pins = gate.inputs.slice(2, 6); // Data inputs [2-5]
        const write_enable = gate.inputs[6].value;
        const clock_pin = gate.inputs[7].value;
        const data_out_pins = gate.outputs.slice(0, 4);

        // Calculate address
        const address = a0 + (a1 << 1);

        // Initialize clock state if not exists
        if (!this.flipFlopState.has(gate.id)) {
          this.flipFlopState.set(gate.id, { lastClock: 0, qValue: 0 });
        }

        const state = this.flipFlopState.get(gate.id)!;

        // Write operation on rising clock edge
        if (state.lastClock === 0 && clock_pin === 1 && write_enable === 1) {
          const data_in = this.pinsToNumber(data_in_pins) & 0xf; // 4-bit mask
          memory[address] = data_in;
        }

        // Read operation (combinational - always active)
        const data_out = memory[address] & 0xf; // Ensure 4-bit
        const old_data_out = this.pinsToNumber(data_out_pins);
        if (data_out !== old_data_out) {
          this.numberToPins(data_out, data_out_pins);
          data_out_pins.forEach((p) => this.pins.set(p.id, p));
          data_out_pins.forEach((p) => this.propagateValue(p.id));
        }

        // Update last clock state
        state.lastClock = clock_pin;
        this.flipFlopState.set(gate.id, state);
        break;
      }

      case "FULL-ADDER": {
        const a = gate.inputs[0].value;
        const b = gate.inputs[1].value;
        const cin = gate.inputs[2].value;
        const sum_out = gate.outputs[0];
        const cout_out = gate.outputs[1];

        // Full adder logic: Sum = A ⊕ B ⊕ Cin, Cout = AB + Cin(A ⊕ B)
        const sum = (a ^ b ^ cin) as PinValue;
        const cout = ((a & b) | (cin & (a ^ b))) as PinValue;

        if (sum_out.value !== sum) {
          sum_out.value = sum;
          this.pins.set(sum_out.id, sum_out);
          this.propagateValue(sum_out.id);
        }

        if (cout_out.value !== cout) {
          cout_out.value = cout;
          this.pins.set(cout_out.id, cout_out);
          this.propagateValue(cout_out.id);
        }
        break;
      }

      case "2BIT-ADDER": {
        const a1 = gate.inputs[0].value;
        const a0 = gate.inputs[1].value;
        const b1 = gate.inputs[2].value;
        const b0 = gate.inputs[3].value;
        const cin = gate.inputs[4].value;
        const sum1_out = gate.outputs[0];
        const sum0_out = gate.outputs[1];
        const cout_out = gate.outputs[2];

        // 2-bit addition using full adder logic
        // Bit 0: Sum0 = A0 ⊕ B0 ⊕ Cin, C0 = A0B0 + Cin(A0 ⊕ B0)
        const sum0 = (a0 ^ b0 ^ cin) as PinValue;
        const c0 = ((a0 & b0) | (cin & (a0 ^ b0))) as PinValue;

        // Bit 1: Sum1 = A1 ⊕ B1 ⊕ C0, Cout = A1B1 + C0(A1 ⊕ B1)
        const sum1 = (a1 ^ b1 ^ c0) as PinValue;
        const cout = ((a1 & b1) | (c0 & (a1 ^ b1))) as PinValue;

        if (sum1_out.value !== sum1) {
          sum1_out.value = sum1;
          this.pins.set(sum1_out.id, sum1_out);
          this.propagateValue(sum1_out.id);
        }

        if (sum0_out.value !== sum0) {
          sum0_out.value = sum0;
          this.pins.set(sum0_out.id, sum0_out);
          this.propagateValue(sum0_out.id);
        }

        if (cout_out.value !== cout) {
          cout_out.value = cout;
          this.pins.set(cout_out.id, cout_out);
          this.propagateValue(cout_out.id);
        }
        break;
      }

      case "SR-LATCH": {
        const s_in = gate.inputs[0].value; // Set input
        const r_in = gate.inputs[1].value; // Reset input
        const q_out = gate.outputs[0]; // Q output
        const qbar_out = gate.outputs[1]; // Q̄ output

        // Initialize state if not exists
        if (!this.flipFlopState.has(gate.id)) {
          this.flipFlopState.set(gate.id, { lastClock: 0, qValue: 0 });
        }

        const state = this.flipFlopState.get(gate.id)!;

        // SR Latch logic (active low inputs)
        // S=0, R=1: Set (Q=1, Q̄=0)
        // S=1, R=0: Reset (Q=0, Q̄=1)
        // S=1, R=1: Hold previous state
        // S=0, R=0: Forbidden (both outputs would be 1)

        let newQ: PinValue;
        let newQbar: PinValue;

        if (s_in === 0 && r_in === 1) {
          // Set state
          newQ = 1;
          newQbar = 0;
          state.qValue = 1;
        } else if (s_in === 1 && r_in === 0) {
          // Reset state
          newQ = 0;
          newQbar = 1;
          state.qValue = 0;
        } else if (s_in === 1 && r_in === 1) {
          // Hold state
          newQ = state.qValue;
          newQbar = state.qValue === 1 ? 0 : 1;
        } else {
          // Forbidden state (S=0, R=0) - both outputs high
          newQ = 1;
          newQbar = 1;
        }

        if (q_out.value !== newQ) {
          q_out.value = newQ;
          this.pins.set(q_out.id, q_out);
          this.propagateValue(q_out.id);
        }

        if (qbar_out.value !== newQbar) {
          qbar_out.value = newQbar;
          this.pins.set(qbar_out.id, qbar_out);
          this.propagateValue(qbar_out.id);
        }

        this.flipFlopState.set(gate.id, state);
        break;
      }

      // --- END NEW LOGIC ---
    }

    // SWITCH and LED gates do not evaluate, they are controlled externally or just display values.
  }

  // --- Utility functions for rendering ---

  saveState(): SavedChallengeState {
    const savedGates: SavedGateState[] = Array.from(this.gates.values()).map(
      (gate) => ({
        id: gate.id,
        type: gate.type,
        x: gate.x,
        y: gate.y,
      }),
    );

    const savedWires: SavedWireState[] = Array.from(this.wires.values())
      .map((wire) => {
        const fromPin = this.pins.get(wire.fromPinId);
        const toPin = this.pins.get(wire.toPinId);
        const fromGate = fromPin ? this.gates.get(fromPin.gateId) : null;
        const toGate = toPin ? this.gates.get(toPin.gateId) : null;

        if (!fromGate || !toGate) {
          // This should not happen in a consistent state
          return null;
        }

        const fromPinIndex = fromGate.outputs.findIndex(
          (p) => p.id === wire.fromPinId,
        );
        const toPinIndex = toGate.inputs.findIndex(
          (p) => p.id === wire.toPinId,
        );

        return {
          fromGateId: fromGate.id,
          fromPinIndex,
          toGateId: toGate.id,
          toPinIndex,
        };
      })
      .filter((w) => w !== null) as SavedWireState[];

    return {
      gates: savedGates,
      wires: savedWires,
    };
  }

  loadState(state: SavedChallengeState) {
    this.gates.clear();
    this.wires.clear();
    this.pins.clear();
    this.ramState.clear();
    this.flipFlopState.clear();

    const gateIdMap = new Map<string, string>(); // old ID -> new ID

    state.gates.forEach((savedGate) => {
      const newGate = this.addGate(savedGate.type, savedGate.x, savedGate.y);
      gateIdMap.set(savedGate.id, newGate.id);
    });

    state.wires.forEach((savedWire) => {
      const fromGateId = gateIdMap.get(savedWire.fromGateId);
      const toGateId = gateIdMap.get(savedWire.toGateId);

      if (fromGateId && toGateId) {
        const fromGate = this.gates.get(fromGateId);
        const toGate = this.gates.get(toGateId);

        if (fromGate && toGate) {
          const fromPin = fromGate.outputs[savedWire.fromPinIndex];
          const toPin = toGate.inputs[savedWire.toPinIndex];

          if (fromPin && toPin) {
            this.addWire(fromPin.id, toPin.id);
          }
        }
      }
    });
  }

  getPinCoords(pinId: string): { x: number; y: number } {
    const pin = this.pins.get(pinId);
    if (!pin) return { x: 0, y: 0 };

    const gate = this.gates.get(pin.gateId)!;
    const isInput = pin.type === "input";
    const pinIndex = isInput
      ? gate.inputs.findIndex((p) => p.id === pinId)
      : gate.outputs.findIndex((p) => p.id === pinId);

    // Updated for new gate sizes (70x90) and centered output pins
    let inputYOffset = 35 + pinIndex * 20; // Input pins start at y=35
    // Special case for LED gates - center input pin vertically
    if (gate.type === "LED" && isInput) {
      inputYOffset = 45; // Center LED input pin at y=45
    }
    // Special case for NOT gates - center input pin vertically
    if (gate.type === "NOT" && isInput) {
      inputYOffset = 45; // Center NOT input pin at y=45
    }
    const outputYOffset = 45; // Output pins centered at y=45
    const yOffset = isInput ? inputYOffset : outputYOffset;

    // Updated x offsets for new gate width
    let xOffset = isInput ? -5 : 75;

    // Special case for NAND gates - use bubble position
    if (gate.type === "NAND" && !isInput) {
      xOffset = 60; // Position of the NAND bubble
    }

    // Special case for NOT gates - use bubble position
    if (gate.type === "NOT" && !isInput) {
      xOffset = 55; // Position of the NOT bubble
    }

    return { x: gate.x + xOffset, y: gate.y + yOffset };
  }

  public async checkSolution(
    challenge: Challenge,
  ): Promise<{ success: boolean; message: string }> {
    const switches = [...this.gates.values()].filter(
      (g) => g.type === "SWITCH",
    );
    const leds = [...this.gates.values()].filter((g) => g.type === "LED");

    // 1. Check if the user has the correct number of I/O components
    if (switches.length !== challenge.verification.inputs) {
      return {
        success: false,
        message: `Incorrect setup. Expected ${challenge.verification.inputs} Switches, but found ${switches.length}.`,
      };
    }
    if (leds.length !== challenge.verification.outputs) {
      return {
        success: false,
        message: `Incorrect setup. Expected ${challenge.verification.outputs} LEDs, but found ${leds.length}.`,
      };
    }

    // 2. Iterate through the truth table and test each case
    for (const testCase of challenge.verification.truthTable) {
      // Set switch values according to the test case
      for (let i = 0; i < testCase.inputs.length; i++) {
        const switchGate = switches[i];
        const requiredValue = testCase.inputs[i];
        const currentOutput = switchGate.outputs[0].value;

        if (currentOutput !== requiredValue) {
          this.toggleSwitch(switchGate.id); // This will trigger a simulation propagation
        }
      }

      // Wait for the simulation to settle. setTimeout is a simple way to do this.
      await new Promise((resolve) => setTimeout(resolve, 10));

      // Check the LED outputs
      for (let i = 0; i < testCase.outputs.length; i++) {
        const ledGate = leds[i];
        const expectedValue = testCase.outputs[i];
        const actualValue = ledGate.inputs[0].value;

        if (actualValue !== expectedValue) {
          const inputStr = testCase.inputs.join(", ");
          return {
            success: false,
            message: `Test failed for inputs [${inputStr}]. Expected output was ${expectedValue}, but your circuit produced ${actualValue}.`,
          };
        }
      }
    }

    // 3. If all tests passed
    return { success: true, message: "Congratulations! All tests passed." };
  }
}
