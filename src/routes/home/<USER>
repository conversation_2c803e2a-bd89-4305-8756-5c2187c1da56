<script lang="ts">
  import { goto } from '$app/navigation';
  import ThemeToggle from '$lib/components/ThemeToggle.svelte';

  function startLearning() {
    goto('/learn');
  }

  function goHome() {
    goto('/home');
  }

  function handleBrandKeydown(event: KeyboardEvent) {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      goHome();
    }
  }
</script>

<!-- Home Page Navigation Bar -->
<header class="home-header">
  <div class="header-content">
    <div class="brand-section">
      <button class="brand-title" on:click={goHome} on:keydown={handleBrandKeydown} aria-label="Go to ByteCrafted home page" title="Go to home page">
        ByteCrafted
      </button>
    </div>

    <nav class="header-nav">
      <button class="nav-button launch-button" on:click={startLearning}>
        <span class="nav-icon">🚀</span>
        Launch App
      </button>
      <ThemeToggle />
    </nav>
  </div>
</header>

<div class="home-container">
  <div class="hero-section">
    <div class="hero-content">
      <h1 class="hero-title">
        <span class="brand">ByteCrafted</span>
        <span class="subtitle">Build an 8-Bit Computer from Scratch</span>
      </h1>
      
      <p class="hero-description">
        Master computer architecture through hands-on construction. Start with basic logic gates 
        and progressively build toward a complete, programmable 8-bit computer running custom code.
      </p>

      <button class="cta-button" on:click={startLearning}>
        Start Building Your Computer
        <span class="cta-arrow">→</span>
      </button>

      <div class="journey-preview">
        <div class="journey-step">
          <div class="step-icon">⚡</div>
          <div class="step-content">
            <h3>Logic Gates</h3>
            <p>Build AND, OR, XOR from NAND gates</p>
          </div>
        </div>
        
        <div class="journey-arrow">→</div>
        
        <div class="journey-step">
          <div class="step-icon">🔢</div>
          <div class="step-content">
            <h3>Arithmetic Circuits</h3>
            <p>Create adders and ALU components</p>
          </div>
        </div>
        
        <div class="journey-arrow">→</div>
        
        <div class="journey-step">
          <div class="step-icon">💾</div>
          <div class="step-content">
            <h3>Memory Systems</h3>
            <p>Build registers, RAM, and storage</p>
          </div>
        </div>
        
        <div class="journey-arrow">→</div>
        
        <div class="journey-step">
          <div class="step-icon">🖥️</div>
          <div class="step-content">
            <h3>Complete Computer</h3>
            <p>Assemble your programmable 8-bit system</p>
          </div>
        </div>
      </div>

      <div class="features">
        <div class="feature">
          <h4>🛠️ Learning by Building</h4>
          <p>Construct each component from previously mastered parts</p>
        </div>
        <div class="feature">
          <h4>🎯 First Principles</h4>
          <p>Understand how computers work from the ground up</p>
        </div>
        <div class="feature">
          <h4>💻 Interactive Workbench</h4>
          <p>Visual circuit builder with real-time simulation</p>
        </div>
      </div>
    </div>
  </div>

  <div class="background-pattern"></div>
</div>

<style>
  /* Home Page Navigation Bar Styles - Matching Learn Section */
  .home-header {
    background-color: var(--bg-secondary);
    border-bottom: 1px solid var(--border-default);
    padding: var(--spacing-3) var(--spacing-6);
    position: sticky;
    top: 0;
    z-index: var(--z-sticky);
    box-shadow: var(--shadow-md);
    backdrop-filter: blur(8px);
  }

  .header-content {
    max-width: 1400px;
    margin: 0 auto;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .brand-section {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-1);
  }

  .brand-title {
    font-size: var(--font-size-xl);
    font-weight: var(--font-weight-bold);
    background: linear-gradient(135deg, var(--color-accent), var(--text-purple), var(--color-success));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin: 0;
    line-height: var(--line-height-tight);
    border: none;
    padding: 0;
    cursor: pointer;
    transition: all var(--transition-fast);
    text-decoration: none;
    font-family: inherit;
    background-color: transparent;
  }

  .brand-title:hover {
    transform: translateY(-1px);
    filter: brightness(1.1);
  }

  .brand-title:focus-visible {
    outline: 2px solid var(--color-accent);
    outline-offset: 3px;
    border-radius: var(--radius-sm);
  }

  .brand-title:active {
    transform: translateY(0);
  }



  .header-nav {
    display: flex;
    gap: var(--spacing-3);
  }

  .nav-button {
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
    background-color: var(--bg-elevated);
    color: var(--text-primary);
    border: 1px solid var(--border-default);
    padding: var(--spacing-2) var(--spacing-4);
    border-radius: var(--radius-md);
    cursor: pointer;
    transition: all var(--transition-fast);
    font-weight: var(--font-weight-medium);
    font-size: var(--font-size-sm);
    text-decoration: none;
    white-space: nowrap;
  }

  .nav-button:hover {
    background-color: var(--bg-surface);
    border-color: var(--color-accent);
    transform: translateY(-1px);
    box-shadow: var(--shadow-sm);
    color: var(--text-primary);
  }

  .nav-button:focus-visible {
    outline: 2px solid var(--color-accent);
    outline-offset: 2px;
  }

  .nav-icon {
    font-size: var(--font-size-base);
  }

  /* Launch App button - Primary CTA styling */
  .launch-button {
    background: linear-gradient(135deg, var(--color-accent), var(--color-accent-hover));
    border-color: var(--color-accent);
    color: var(--text-inverse);
    font-weight: var(--font-weight-semibold);
  }

  .launch-button:hover {
    background: linear-gradient(135deg, var(--color-accent-hover), var(--color-accent));
    border-color: var(--color-accent-hover);
    color: var(--text-inverse);
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
  }

  .launch-button:active {
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
  }

  .home-container {
    min-height: calc(100vh - 80px); /* Account for navigation bar height */
    background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 50%, var(--bg-tertiary) 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
  }

  .background-pattern {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image:
      radial-gradient(circle at 25% 25%, rgba(88, 166, 255, 0.08) 0%, transparent 50%),
      radial-gradient(circle at 75% 75%, rgba(63, 185, 80, 0.08) 0%, transparent 50%);
    pointer-events: none;
  }

  .hero-section {
    max-width: 1200px;
    padding: var(--spacing-8);
    text-align: center;
    position: relative;
    z-index: 1;
  }

  .hero-title {
    margin-bottom: var(--spacing-8);
  }

  .brand {
    display: block;
    font-size: var(--font-size-5xl);
    font-weight: var(--font-weight-bold);
    background: linear-gradient(135deg, var(--color-accent), var(--text-purple), var(--color-success));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: var(--spacing-2);
    line-height: var(--line-height-tight);
    letter-spacing: -0.02em;
  }

  .subtitle {
    display: block;
    font-size: var(--font-size-xl);
    color: var(--text-primary);
    font-weight: var(--font-weight-normal);
    line-height: var(--line-height-snug);
  }

  .hero-description {
    font-size: var(--font-size-lg);
    color: var(--text-secondary);
    line-height: var(--line-height-relaxed);
    margin-bottom: var(--spacing-12);
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;
  }

  .journey-preview {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 1rem;
    margin-bottom: 3rem;
    flex-wrap: wrap;
  }

  .journey-step {
    background: var(--bg-card);
    border: 1px solid var(--border-default);
    border-radius: 12px;
    padding: 1.5rem;
    min-width: 180px;
    transition: all 0.3s ease;
  }

  .journey-step:hover {
    transform: translateY(-5px);
    border-color: var(--color-accent);
    box-shadow: var(--shadow-lg);
  }

  .step-icon {
    font-size: 2rem;
    margin-bottom: 0.5rem;
  }

  .step-content h3 {
    color: var(--color-accent);
    margin: 0 0 0.5rem 0;
    font-size: 1.1rem;
  }

  .step-content p {
    color: var(--text-secondary);
    margin: 0;
    font-size: 0.9rem;
    line-height: 1.4;
  }

  .journey-arrow {
    color: var(--text-tertiary);
    font-size: 1.5rem;
    font-weight: bold;
  }

  .cta-button {
    background: linear-gradient(135deg, var(--color-accent), var(--color-success));
    color: var(--text-inverse);
    border: none;
    padding: var(--spacing-4) var(--spacing-8);
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-semibold);
    border-radius: var(--radius-xl);
    cursor: pointer;
    transition: all var(--transition-normal);
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-2);
    margin-bottom: var(--spacing-12);
    box-shadow: var(--shadow-lg);
    letter-spacing: -0.01em;
  }

  .cta-button:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-2xl);
    background: linear-gradient(135deg, var(--color-accent-hover), var(--color-success-hover));
  }

  .cta-button:focus-visible {
    outline: 3px solid var(--color-accent);
    outline-offset: 3px;
  }

  .cta-arrow {
    transition: transform var(--transition-normal);
    font-size: var(--font-size-xl);
  }

  .cta-button:hover .cta-arrow {
    transform: translateX(4px);
  }

  .features {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    max-width: 900px;
    margin: 0 auto;
  }

  .feature {
    text-align: left;
    background: var(--bg-card);
    padding: 1.5rem;
    border-radius: 8px;
    border: 1px solid var(--border-default);
  }

  .feature h4 {
    color: var(--color-warning);
    margin: 0 0 0.5rem 0;
    font-size: 1.1rem;
  }

  .feature p {
    color: var(--text-secondary);
    margin: 0;
    line-height: 1.5;
  }

  /* Responsive Design for Navigation */
  @media (max-width: 768px) {
    .home-header {
      padding: var(--spacing-2) var(--spacing-4);
    }

    .header-content {
      flex-direction: column;
      gap: var(--spacing-3);
      align-items: center;
    }

    .brand-section {
      text-align: center;
    }

    .brand-title {
      font-size: var(--font-size-lg);
    }

    .header-nav {
      gap: var(--spacing-2);
    }

    .nav-button {
      padding: var(--spacing-2) var(--spacing-3);
      font-size: var(--font-size-xs);
    }

    .home-container {
      min-height: calc(100vh - 120px); /* Account for larger mobile header */
    }

    .brand {
      font-size: 3rem;
    }

    .subtitle {
      font-size: 1.4rem;
    }

    .hero-description {
      font-size: 1.1rem;
    }

    .journey-preview {
      flex-direction: column;
    }

    .journey-arrow {
      transform: rotate(90deg);
    }

    .features {
      grid-template-columns: 1fr;
    }
  }
</style>
