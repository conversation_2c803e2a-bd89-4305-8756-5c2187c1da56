# ByteCrafted: Complete 8-Bit Computer Curriculum

## Overview

This streamlined curriculum takes learners from basic logic gates to building a complete 8-bit computer with its own programming language. The curriculum is divided into two main sections: **Hardware** (building the physical computer) and **Software** (creating programming tools and applications).

**Total Estimated Time**: 80-100 hours (expanded curriculum)
**Module Count**: 10 modules (4 foundation + 6 extensions)
**Progression**: Beginner → Intermediate → Advanced → Expert

---

# PART I: HARDWARE SECTION

_Building the Physical Computer (Modules 1-7)_

## Current Foundation (Implemented & In Progress)

### Module 1: Basic Logic Gates ✅ **4/4 challenges**

**Learning Objectives**: Master fundamental logic operations and universal gate construction
**Key Concepts**: NAND universality, Boolean algebra, gate construction, <PERSON>'s laws
**Prerequisites**: None
**Estimated Time**: 4-5 hours
**Current Status**:

- ✅ Build AND Gate (from NAND gates) - unlocks AND
- ✅ Build XOR Gate (from NAND gates) - unlocks XOR
- ✅ Build OR Gate (using <PERSON>'s law) - unlocks OR
- ✅ Build NOT Gate (single NAND inverter) - unlocks NOT

### Module 2: Combinational Logic ✅ **3/3 challenges**

**Learning Objectives**: Build arithmetic circuits without memory
**Key Concepts**: Half-adder construction, full-adder progression, carry propagation, multi-bit arithmetic
**Prerequisites**: Basic Logic Gates
**Estimated Time**: 5-6 hours
**Current Status**:

- ✅ Build Half-Adder (using AND and XOR gates) - foundation component
- ✅ Build Full Adder (3-input arithmetic with carry) - unlocks FULL-ADDER
- ✅ Build 2-bit Adder (chained full adders) - unlocks 2BIT-ADDER

### Module 3: Sequential Logic ✅ **2/2 challenges**

**Learning Objectives**: Understand memory and state in digital circuits
**Key Concepts**: D-Latch behavior, SR-Latch construction, cross-coupled feedback, bistable circuits
**Prerequisites**: Combinational Logic
**Estimated Time**: 5-6 hours
**Current Status**:

- ✅ Observe D-Latch (understanding memory behavior) - foundation concept
- ✅ Build SR Latch (cross-coupled NAND memory) - unlocks SR-LATCH

### Module 4: Registers & Memory ✅ **4/4 challenges**

**Learning Objectives**: Build multi-bit storage and memory systems
**Key Concepts**: Edge-triggered storage, register construction, memory hierarchy, addressable storage
**Prerequisites**: Sequential Logic
**Estimated Time**: 6-8 hours
**Current Status**:

- ✅ Build D Flip-Flop (edge-triggered memory) - unlocks D-FLIP-FLOP
- ✅ Build 4-bit Register (parallel storage) - unlocks 4BIT-REGISTER
- ✅ Build 8-bit Register (byte-wide storage) - unlocks 8BIT-REGISTER
- ✅ Build Simple Memory (4×4 RAM with addressing) - unlocks 4X4-RAM

---

## Planned Hardware Extensions (Modules 5-7)

### Module 5: Arithmetic Circuits 🚧 **Not Yet Implemented**

**Learning Objectives**: Build essential arithmetic components for the CPU
**Key Concepts**: Multi-bit addition, data routing, address decoding
**Prerequisites**: Registers & Memory (Module 4)
**Estimated Time**: 6-8 hours

**Planned Challenges**:

1. **Build Full Adder**

   - _Description_: Construct a full adder that adds three bits (A, B, Carry-in)
   - _Tools_: ['SWITCH', 'LED', 'AND', 'XOR', 'OR', 'HALF-ADDER']
   - _Inputs_: 3 (A, B, Carry-in) | _Outputs_: 2 (Sum, Carry-out)
   - _Unlocks_: 'FULL-ADDER'

2. **Build 8-bit Adder**

   - _Description_: Chain full adders to create 8-bit binary addition
   - _Tools_: ['SWITCH', 'LED', 'FULL-ADDER']
   - _Inputs_: 17 (A7-A0, B7-B0, Carry-in) | _Outputs_: 9 (Sum7-Sum0, Carry-out)
   - _Unlocks_: '8BIT-ADDER'

3. **Build 4:1 Multiplexer**

   - _Description_: Create data selector for CPU routing
   - _Tools_: ['SWITCH', 'LED', 'AND', 'OR', 'NOT']
   - _Inputs_: 6 (Input0-3, Select1, Select0) | _Outputs_: 1 (Output)
   - _Unlocks_: '4TO1-MUX'

4. **Build 3:8 Decoder**
   - _Description_: Create address decoder for memory and register selection
   - _Tools_: ['SWITCH', 'LED', 'AND', 'NOT']
   - _Inputs_: 4 (A2, A1, A0, Enable) | _Outputs_: 8 (Select7-0)
   - _Unlocks_: '3TO8-DECODER'

### Module 6: Arithmetic Logic Unit (ALU) 🚧 **Not Yet Implemented**

**Learning Objectives**: Build the computational heart of the CPU
**Key Concepts**: Multi-operation arithmetic unit, flag generation, operation selection
**Prerequisites**: Arithmetic Circuits (Module 5)
**Estimated Time**: 8-10 hours

**Planned Challenges**:

1. **Build 1-bit ALU Slice**

   - _Description_: Create fundamental 1-bit ALU supporting AND, OR, ADD operations
   - _Tools_: ['SWITCH', 'LED', 'AND', 'OR', 'FULL-ADDER', '4TO1-MUX']
   - _Inputs_: 5 (A, B, Carry-in, Op1, Op0) | _Outputs_: 2 (Result, Carry-out)
   - _Unlocks_: '1BIT-ALU'

2. **Build 8-bit ALU**

   - _Description_: Chain 8 ALU slices to create complete arithmetic logic unit
   - _Tools_: ['SWITCH', 'LED', '1BIT-ALU']
   - _Inputs_: 18 (A7-0, B7-0, Op1, Op0) | _Outputs_: 8 (Result7-0)
   - _Unlocks_: '8BIT-ALU'

3. **Add Subtraction Support**

   - _Description_: Implement subtraction using two's complement addition
   - _Tools_: ['SWITCH', 'LED', '8BIT-ALU', 'XOR']
   - _Inputs_: 19 (A7-0, B7-0, Op2, Op1, Op0) | _Outputs_: 8 (Result7-0)
   - _Unlocks_: '8BIT-ALU-COMPLETE'

4. **Implement Status Flags**
   - _Description_: Add Zero, Carry, Overflow, and Negative flag outputs
   - _Tools_: ['SWITCH', 'LED', '8BIT-ALU-COMPLETE', 'OR', 'XOR']
   - _Inputs_: 19 (A7-0, B7-0, Op2-0) | _Outputs_: 12 (Result7-0, Flags3-0)
   - _Unlocks_: 'ALU-WITH-FLAGS'

### Module 7: Complete CPU 🚧 **Not Yet Implemented**

**Learning Objectives**: Integrate all components into a working 8-bit processor
**Key Concepts**: CPU architecture, instruction cycle, control unit, datapath integration
**Prerequisites**: ALU (Module 6), Registers & Memory (Module 4)
**Estimated Time**: 12-15 hours

**Planned Challenges**:

1. **Build Register File**

   - _Description_: Create 8-register file with dual read ports and one write port
   - _Tools_: ['SWITCH', 'LED', 'D-FLIP-FLOP', '3TO8-DECODER', '8TO1-MUX']
   - _Inputs_: 17 (Write-data7-0, Write-addr2-0, Read-addr-A2-0, Read-addr-B2-0, Write-enable, Clock)
   - _Outputs_: 16 (Read-data-A7-0, Read-data-B7-0)
   - _Unlocks_: 'REGISTER-FILE'

2. **Build Program Counter**

   - _Description_: Create 8-bit program counter with increment and load capabilities
   - _Tools_: ['CLOCK', 'SWITCH', 'LED', 'D-FLIP-FLOP', '8BIT-ADDER', '4TO1-MUX']
   - _Inputs_: 11 (Jump-addr7-0, PC-load, PC-inc, Clock)
   - _Outputs_: 8 (PC7-0)
   - _Unlocks_: 'PROGRAM-COUNTER'

3. **Build Instruction Decoder**

   - _Description_: Create decoder that identifies instruction types from opcode
   - _Tools_: ['SWITCH', 'LED', '3TO8-DECODER', 'AND', 'OR']
   - _Inputs_: 8 (Instruction7-0)
   - _Outputs_: 8 (Load, Store, Add, Sub, Jump, JumpZero, Halt, Nop)
   - _Unlocks_: 'INSTRUCTION-DECODER'

4. **Build Control Unit**

   - _Description_: Generate control signals for datapath based on instruction
   - _Tools_: ['CLOCK', 'SWITCH', 'LED', 'INSTRUCTION-DECODER', 'D-FLIP-FLOP']
   - _Inputs_: 12 (Instruction7-0, Flags3-0, Clock, Reset)
   - _Outputs_: 16 (Control-signals15-0)
   - _Unlocks_: 'CONTROL-UNIT'

5. **Build CPU Datapath**

   - _Description_: Connect ALU, register file, and memory with data buses
   - _Tools_: ['SWITCH', 'LED', 'ALU-WITH-FLAGS', 'REGISTER-FILE', '4TO1-MUX']
   - _Inputs_: 24 (Data-sources, ALU-inputs, Register-selects, Control-signals)
   - _Outputs_: 16 (ALU-result7-0, Register-outputs7-0)
   - _Unlocks_: 'CPU-DATAPATH'

6. **Integrate Complete CPU**
   - _Description_: Combine all components into working 8-bit CPU
   - _Tools_: ['CLOCK', 'SWITCH', 'LED', 'CPU-DATAPATH', 'CONTROL-UNIT', 'PROGRAM-COUNTER']
   - _Inputs_: 16 (Memory-interface7-0, Clock, Reset, Input-data7-0)
   - _Outputs_: 16 (Memory-interface7-0, Output-data7-0)
   - _Unlocks_: '8BIT-CPU'

---

# PART II: SOFTWARE SECTION

_Programming Tools and Applications (Modules 8-10)_

### Module 8: Assembly Language & Assembler 🚧 **Not Yet Implemented**

**Learning Objectives**: Create programming interface for the built CPU
**Key Concepts**: Instruction set design, assembly syntax, code generation
**Prerequisites**: Complete CPU (Module 7)
**Estimated Time**: 10-12 hours

**Planned Challenges**:

1. **Define Instruction Set Architecture**

   - _Description_: Design 16-instruction set covering arithmetic, logic, memory, and control
   - _Tools_: Web-based design interface
   - _Inputs_: Instruction requirements | _Outputs_: Complete ISA specification
   - _Unlocks_: 'CPU-ISA'

2. **Create Assembly Language Syntax**

   - _Description_: Define human-readable syntax for all CPU instructions
   - _Tools_: Web-based language design interface
   - _Inputs_: ISA specification | _Outputs_: Assembly language grammar
   - _Unlocks_: 'ASSEMBLY-SYNTAX'

3. **Build Assembler Parser**

   - _Description_: Create parser that converts assembly code to machine code
   - _Tools_: Web-based programming interface
   - _Inputs_: Assembly source code | _Outputs_: Binary machine code
   - _Unlocks_: 'ASSEMBLER'

4. **Add Symbol Table Support**

   - _Description_: Implement labels and forward references in assembler
   - _Tools_: Web-based programming interface with parser
   - _Inputs_: Assembly with labels | _Outputs_: Resolved machine code
   - _Unlocks_: 'ASSEMBLER-COMPLETE'

5. **Create Assembly IDE**
   - _Description_: Build integrated development environment with editor and debugger
   - _Tools_: Web-based programming interface
   - _Inputs_: Assembly programs | _Outputs_: Executable code with debugging
   - _Unlocks_: 'ASSEMBLY-IDE'

### Module 9: High-Level Programming Language 🚧 **Not Yet Implemented**

**Learning Objectives**: Create ByteScript, a simple programming language for the CPU
**Key Concepts**: Language design, compiler implementation, code generation
**Prerequisites**: Assembly Language & Assembler (Module 8)
**Estimated Time**: 12-15 hours

**Planned Challenges**:

1. **Design ByteScript Syntax**

   - _Description_: Create syntax for variables, expressions, control flow, and functions
   - _Tools_: Web-based language design interface
   - _Inputs_: Language requirements | _Outputs_: Complete grammar specification
   - _Unlocks_: 'BYTESCRIPT-SYNTAX'

2. **Build ByteScript Parser**

   - _Description_: Implement parser that builds abstract syntax trees
   - _Tools_: Web-based programming interface
   - _Inputs_: ByteScript source code | _Outputs_: Abstract syntax tree
   - _Unlocks_: 'BYTESCRIPT-PARSER'

3. **Implement Code Generator**

   - _Description_: Generate assembly code from ByteScript programs
   - _Tools_: Web-based programming interface with parser
   - _Inputs_: Abstract syntax tree | _Outputs_: Assembly code
   - _Unlocks_: 'BYTESCRIPT-COMPILER'

4. **Create Standard Library**

   - _Description_: Build library with I/O, math, and utility functions
   - _Tools_: Web-based programming interface with compiler
   - _Inputs_: Library specifications | _Outputs_: Standard library implementation
   - _Unlocks_: 'BYTESCRIPT-STDLIB'

5. **Build ByteScript IDE**
   - _Description_: Create complete development environment for ByteScript
   - _Tools_: Web-based programming interface
   - _Inputs_: ByteScript programs | _Outputs_: Executable applications
   - _Unlocks_: 'BYTESCRIPT-IDE'

### Module 10: Applications & System Integration 🚧 **Not Yet Implemented**

**Learning Objectives**: Build complete applications running on the computer system
**Key Concepts**: Application development, system integration, user interfaces
**Prerequisites**: High-Level Programming Language (Module 9)
**Estimated Time**: 10-12 hours

**Planned Challenges**:

1. **Create System Integration**

   - _Description_: Connect CPU, memory, I/O into unified computer system
   - _Tools_: All previously built components
   - _Inputs_: System specifications | _Outputs_: Complete working computer
   - _Unlocks_: 'COMPLETE-COMPUTER'

2. **Add I/O Interface**

   - _Description_: Implement keyboard input and display output
   - _Tools_: Complete computer system
   - _Inputs_: I/O specifications | _Outputs_: Interactive computer system
   - _Unlocks_: 'IO-COMPUTER'

3. **Build Calculator Application**

   - _Description_: Create interactive calculator in ByteScript
   - _Tools_: ByteScript IDE with I/O computer
   - _Inputs_: Calculator requirements | _Outputs_: Working calculator app
   - _Unlocks_: 'CALCULATOR-APP'

4. **Create Text Editor**

   - _Description_: Build simple text editor with file operations
   - _Tools_: ByteScript IDE with I/O computer
   - _Inputs_: Editor requirements | _Outputs_: Working text editor
   - _Unlocks_: 'TEXT-EDITOR'

5. **Design Simple Game**
   - _Description_: Create interactive game (number guessing, tic-tac-toe)
   - _Tools_: ByteScript IDE with I/O computer
   - _Inputs_: Game specifications | _Outputs_: Working game application
   - _Unlocks_: 'GAME-APP'

---

## Learning Path Summary

**Current Foundation (Modules 1-4)**: 20-25 hours (13/13 challenges implemented) ✅
**Planned Hardware Extensions (Modules 5-7)**: 26-33 hours (not yet implemented)
**Planned Software Section (Modules 8-10)**: 32-39 hours (not yet implemented)
**Total Curriculum**: 80-100 hours (expanded from original plan)

## Prerequisites Flow

### Hardware Path:

```
Basic Gates → Combinational Logic → Sequential Logic → Registers & Memory
     ↓              ↓                    ↓                ↓
     └─────→ Arithmetic Circuits ────→ ALU ────→ Complete CPU
```

### Software Path:

```
Complete CPU → Assembly Language & Assembler → High-Level Language → Applications
```

## Current Implementation Status & Next Steps

### ✅ **Foundation Modules (Complete)**

- **Module 1**: 4/4 challenges implemented (AND, XOR, OR, NOT gates)
- **Module 2**: 3/3 challenges implemented (Half-adder, Full-adder, 2-bit Adder)
- **Module 3**: 2/2 challenges implemented (D-Latch observation, SR-Latch construction)
- **Module 4**: 4/4 challenges implemented (D-Flip-Flop, 4-bit Register, 8-bit Register, 4×4 RAM)

### 🚀 **Next Development Priorities**

1. **Module 5: Arithmetic Circuits**: Build essential CPU arithmetic components
2. **Module 6: ALU**: Create the computational heart of the processor
3. **Module 7: Complete CPU**: Integrate all components into working processor
4. **Advanced Features**: Optimization, testing, and polish

### 🎯 **Curriculum Design Principles**

- **Progressive Building**: Each challenge builds on previous components
- **Hands-on Learning**: Every concept reinforced through practical construction
- **Modular Design**: Students create reusable components for future modules
- **Clear Verification**: Each component thoroughly tested before progression
- **Real-world Relevance**: Components mirror actual computer architecture

### 📈 **Complete Foundation Benefits**

- **Comprehensive Logic**: All fundamental Boolean operations (AND, OR, NOT, XOR, NAND)
- **Arithmetic Mastery**: From half-adders to multi-bit arithmetic circuits
- **Memory Understanding**: From basic latches to addressable RAM systems
- **Progressive Building**: Each component builds naturally on previous concepts
- **Real-world Relevance**: Components mirror actual computer architecture

---

## About ByteCrafted

This enhanced curriculum provides a complete journey from basic digital logic to a functioning computer system with its own programming language. ByteCrafted maintains its core philosophy of **learning through building** while providing comprehensive coverage of essential computer architecture concepts.

**Current Status**: Foundation modules (1-4) are complete, with all 13 planned challenges implemented and tested. The platform features a robust workbench environment, realistic component visualization, comprehensive verification systems, and complete theory content.

**Next Milestone**: Begin Module 5 (Arithmetic Circuits) to build essential CPU arithmetic components, leading toward complete 8-bit processor construction.
